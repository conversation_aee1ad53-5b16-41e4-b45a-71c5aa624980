/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * ➕ 四分侧合操作类 (QuarterSplitMerger.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现四分割球体合并的功能，通过快速分身创建四个球体然后合并：                          │
 * │                                                                                     │
 * │ 🎯 核心功能:                                                                        │
 * │   • 快速四分 - 通过两次连续分身创建四个球体                                          │
 * │   • 侧向合并 - 向侧面方向移动进行合并                                                │
 * │   • 精确定位 - 使用修正值确保准确的最终位置                                          │
 * │   • 参数验证 - 执行前验证所有参数的有效性                                            │
 * │                                                                                     │
 * │ 🔄 操作流程:                                                                        │
 * │   1. 初始分身 - 释放鼠标后连续执行两次分身创建四个球体                               │
 * │   2. 准备移动 - 重新按下鼠标并移动到中心位置                                         │
 * │   3. 计算位置 - 根据方向向量和修正值计算最终目标位置                                 │
 * │   4. 执行移动 - 移动到计算出的最终位置                                               │
 * │   5. 完成合并 - 释放鼠标并执行最终分身序列                                           │
 * │                                                                                     │
 * │ ⚙️ 特殊功能:                                                                        │
 * │   • 多模式支持 - 提供快速、精确、平衡三种预设模式                                    │
 * │   • 参数验证 - 自动验证鼠标位置、半径、延迟等参数                                    │
 * │   • 智能推荐 - 根据不同场景提供优化的参数设置                                        │
 * │   • 修正算法 - 使用70像素基础修正值进行精确定位                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 快速执行 - 优化的分身序列，执行速度快
 * • 高精度 - 精确的位置计算和修正算法
 * • 可配置 - 支持多种参数调整和模式切换
 * • 健壮性 - 完善的参数验证和错误处理
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 位置计算和输入控制
 * • 使用 QuarterSplitConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局设置
 *
 * 💡 使用技巧:
 * • 快速模式适合网络延迟低的环境
 * • 精确模式适合需要高精度的场景
 * • 平衡模式适合大多数情况
 * • 可根据游戏状态动态调整参数
 *
 * ⚠️ 注意事项:
 * • 分身速度过快可能导致操作失败
 * • 需要确保有足够的操作空间
 * • 避免在球体密集区域使用
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 四分侧合操作类 - 处理四分割球体合并的功能
    /// 这个类实现了四分侧合的完整操作流程
    /// </summary>
    public class QuarterSplitMerger
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.QuarterSplitConfig _config;       // 四分配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;      // 全局配置

        /// <summary>
        /// 构造函数 - 初始化四分侧合操作器
        /// </summary>
        /// <param name="config">四分配置</param>
        /// <param name="globalConfig">全局配置</param>
        public QuarterSplitMerger(BallMergingConfiguration.QuarterSplitConfig config, 
                                 BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
        }

        /// <summary>
        /// 执行四分侧合操作的主函数
        /// 这是四分功能的入口点
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形区域</param>
        /// <param name="directionVector">移动方向向量</param>
        /// <returns>操作是否成功</returns>
        public bool ExecuteQuarterSplit(Struct.POINT mousePosition, Struct.RECT windowRect, 
                                       (double x, double y) directionVector)
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 第一步：计算缩放后的半径和修正值
                // 根据窗口大小调整操作半径，确保在不同分辨率下效果一致
                double scaledRadius = BallManipulationCore.ScaleRadius(
                    _config.MovementRadius,                    // 基础移动半径
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 计算位置修正值，用于精确定位（四分使用70作为基础修正值）
                int correctionValue = BallManipulationCore.ScaleCorrectionValue(
                    70,                                        // 基础修正值
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 第二步：计算移动方向
                var (dx, dy, slope) = BallManipulationCore.CalculateMouseDirection(mousePosition, windowRect);
                var (sx3, sy3) = BallManipulationCore.CalculateMovementParameters(scaledRadius, slope, dx);

                // 第三步：计算方向向量分量
                var (dwX, dwY) = BallManipulationCore.CalculateDirectionVector(directionVector.x, directionVector.y / directionVector.x);

                // 第四步：执行四分序列
                return ExecuteQuarterSplitSequence(mousePosition, (sx3, sy3), (dwX, dwY), correctionValue);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"四分侧合操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行完整的四分侧合序列
        /// 这是四分操作的核心执行逻辑
        /// </summary>
        /// <param name="centerPosition">操作中心位置</param>
        /// <param name="movementVector">移动向量 (sx3, sy3)</param>
        /// <param name="directionVector">方向向量 (dwX, dwY)</param>
        /// <param name="correctionValue">位置修正值</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteQuarterSplitSequence(Struct.POINT centerPosition, 
                                                (double sx3, double sy3) movementVector,
                                                (double dwX, double dwY) directionVector,
                                                int correctionValue)
        {
            try
            {
                // 第一阶段：初始分身（创建四分）
                // 先释放鼠标，然后进行两次快速分身来创建四个球体
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FirstSplitDelay);   // 第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.SecondSplitDelay);  // 第二次分身

                // 第二阶段：准备移动
                BallManipulationCore.SimulateMouseButton(true);   // 按下鼠标左键
                Thread.Sleep(20);                                  // 短暂等待

                // 重置到中心位置
                BallManipulationCore.SetCursorPosition(centerPosition.X, centerPosition.Y);
                Thread.Sleep(_config.DragDelay);                  // 等待拖拽延迟

                // 第三阶段：计算最终位置并移动
                // 根据移动向量和方向向量计算最终目标位置
                int finalX = (int)(centerPosition.X + movementVector.sx3 - directionVector.dwX * correctionValue);
                int finalY = (int)(centerPosition.Y + movementVector.sy3 - directionVector.dwY * correctionValue);

                // 移动到最终位置
                BallManipulationCore.SetCursorPosition(finalX, finalY);
                Thread.Sleep(_config.FinalDragDelay);             // 等待最终拖拽延迟

                // 第四阶段：释放鼠标并完成序列
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键

                // 第五阶段：处理吐球功能（如果启用）
                if (_config.EnableBallEjection)
                {
                    ExecuteBallEjection();  // 执行吐球序列
                }

                // 第六阶段：执行最终分身序列
                ExecuteFinalSplitSequence();

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"四分侧合序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行吐球序列
        /// 启动吐球功能的按键操作
        /// </summary>
        private void ExecuteBallEjection()
        {
            // 获取吐球按键的扫描码
            uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
            // 按下吐球按键（不立即释放，在最终序列中释放）
            API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, 0, 0);
        }

        /// <summary>
        /// 执行最终分身序列
        /// 进行多次分身操作以完成合球
        /// </summary>
        private void ExecuteFinalSplitSequence()
        {
            // 执行20次分身操作，确保球体完全合并
            for (int i = 0; i < 20; i++)
            {
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FinalSplitDelay);
            }

            // 如果启用了吐球功能，完成吐球操作
            if (_config.EnableBallEjection)
            {
                // 获取吐球按键的扫描码
                uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
                // 释放吐球按键
                API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
            }
        }

        /// <summary>
        /// 检查是否可以执行四分侧合操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">当前按下的按键</param>
        /// <param name="windowHandle">当前前台窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改四分侧合的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.QuarterSplitConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                      // 更新启用状态
            _config.EnableBallEjection = newConfig.EnableBallEjection;    // 更新吐球设置
            _config.TriggerKey = newConfig.TriggerKey;                    // 更新触发按键
            _config.MovementRadius = newConfig.MovementRadius;            // 更新移动半径
            _config.FirstSplitDelay = newConfig.FirstSplitDelay;          // 更新第一次分身延迟
            _config.SecondSplitDelay = newConfig.SecondSplitDelay;        // 更新第二次分身延迟
            _config.DragDelay = newConfig.DragDelay;                      // 更新拖拽延迟
            _config.FinalDragDelay = newConfig.FinalDragDelay;            // 更新最终拖拽延迟
            _config.FinalSplitDelay = newConfig.FinalSplitDelay;          // 更新最终分身延迟
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的四分侧合配置信息
        /// </summary>
        /// <returns>当前四分侧合配置</returns>
        public BallMergingConfiguration.QuarterSplitConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }

        /// <summary>
        /// 计算最优移动半径
        /// 根据当前条件计算最适合的操作半径
        /// </summary>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <param name="baseRadius">基础半径值</param>
        /// <returns>优化后的半径</returns>
        public double CalculateOptimalRadius(Struct.RECT windowRect, double baseRadius)
        {
            // 使用核心函数计算缩放后的半径
            return BallManipulationCore.ScaleRadius(
                baseRadius,                               // 基础半径
                windowRect.right - windowRect.left,      // 窗口宽度
                _globalConfig.JoystickSensitivity);       // 摇杆灵敏度
        }

        /// <summary>
        /// 验证四分侧合参数
        /// 在执行前检查参数是否有效
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <returns>参数是否有效</returns>
        public bool ValidateParameters(Struct.POINT mousePosition, Struct.RECT windowRect)
        {
            // 检查鼠标是否在有效的窗口范围内
            if (mousePosition.X < windowRect.left || mousePosition.X > windowRect.right ||
                mousePosition.Y < windowRect.top || mousePosition.Y > windowRect.bottom)
            {
                return false;  // 鼠标位置无效
            }

            // 检查移动半径是否合理
            if (_config.MovementRadius <= 0 || _config.MovementRadius > 1000)
            {
                return false;  // 半径值不合理
            }

            // 检查时间延迟是否合理
            if (_config.FirstSplitDelay < 0 || _config.SecondSplitDelay < 0 ||
                _config.DragDelay < 0 || _config.FinalDragDelay < 0 || _config.FinalSplitDelay < 0)
            {
                return false;  // 延迟值不合理
            }

            return true;  // 所有参数都有效
        }

        /// <summary>
        /// 获取不同游戏场景的推荐设置
        /// 根据不同的使用场景提供优化的配置
        /// </summary>
        /// <param name="scenario">游戏场景类型</param>
        /// <returns>推荐的配置</returns>
        public BallMergingConfiguration.QuarterSplitConfig GetRecommendedSettings(string scenario)
        {
            // 创建新的配置对象
            var config = new BallMergingConfiguration.QuarterSplitConfig();

            // 根据场景类型设置不同的参数
            switch (scenario.ToLower())
            {
                case "fast":  // 快速模式
                    config.FirstSplitDelay = 5;       // 更短的分身延迟
                    config.SecondSplitDelay = 5;      // 更短的分身延迟
                    config.DragDelay = 30;            // 更短的拖拽延迟
                    config.FinalDragDelay = 30;       // 更短的最终拖拽延迟
                    config.FinalSplitDelay = 10;      // 更短的最终分身延迟
                    config.MovementRadius = 80;       // 较小的移动半径
                    break;

                case "precise":  // 精确模式
                    config.FirstSplitDelay = 15;      // 更长的分身延迟
                    config.SecondSplitDelay = 15;     // 更长的分身延迟
                    config.DragDelay = 70;            // 更长的拖拽延迟
                    config.FinalDragDelay = 70;       // 更长的最终拖拽延迟
                    config.FinalSplitDelay = 30;      // 更长的最终分身延迟
                    config.MovementRadius = 120;      // 较大的移动半径
                    break;

                case "balanced":  // 平衡模式
                default:
                    config.FirstSplitDelay = 10;      // 中等分身延迟
                    config.SecondSplitDelay = 10;     // 中等分身延迟
                    config.DragDelay = 50;            // 中等拖拽延迟
                    config.FinalDragDelay = 50;       // 中等最终拖拽延迟
                    config.FinalSplitDelay = 20;      // 中等最终分身延迟
                    config.MovementRadius = 100;      // 中等移动半径
                    break;
            }

            return config;  // 返回推荐配置
        }
    }
}
