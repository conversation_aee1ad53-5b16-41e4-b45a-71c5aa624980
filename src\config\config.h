/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 配置管理模块头文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 所有配置参数的数据结构定义
 * • 配置管理函数的声明
 * • 基于C#版本的完整配置系统移植
 * 
 * 🎯 配置分类:
 * • 🔺 三角合球配置 - 三角形阵型的球体合并参数
 * • ⭕ 中分配置 - 中心分割合球参数
 * • ➕ 四分配置 - 四分割合球参数
 * • ↩️ 后仰配置 - 后仰操作参数
 * • 🎛️ 杠杆宏配置 - 杠杆宏操作参数
 * • 🔄 旋转配置 - 各种旋转操作参数
 * • 🤖 宏操作配置 - 分割宏和连续动作参数
 * • 🌐 全局配置 - 全局设置参数
 * 
 * ✨ 设计特点:
 * • 类型安全的配置管理
 * • 默认值设置和参数验证
 * • 配置文件保存/加载支持
 * • 分类组织便于管理
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <stdbool.h>
#include <stdint.h>
#include <windows.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 配置常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define CONFIG_FILE_NAME "ball_merging_config.ini"
#define MAX_WINDOW_TITLE 256
#define MAX_SECTION_NAME 64
#define MAX_KEY_NAME 64
#define MAX_VALUE_STRING 128

// 默认按键定义（虚拟键码）
#define DEFAULT_SPLIT_KEY 'E'              // 默认分身键
#define DEFAULT_EJECT_KEY 'Q'              // 默认吐球键
#define DEFAULT_TRIGGER_KEY_1 '1'          // 默认触发键1
#define DEFAULT_TRIGGER_KEY_2 '2'          // 默认触发键2
#define DEFAULT_TRIGGER_KEY_3 '3'          // 默认触发键3

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 基础配置结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 🌐 全局配置结构
 * 包含所有全局设置参数
 */
typedef struct {
    BYTE split_key;                        // 分身键
    BYTE ball_eject_key;                   // 吐球键
    int joystick_sensitivity;              // 摇杆灵敏度（1-200）
    char window_title[MAX_WINDOW_TITLE];   // 目标窗口标题
    HWND window_handle;                    // 窗口句柄
    bool auto_detect_window;               // 是否自动检测窗口
    int base_delay;                        // 基础延迟时间（毫秒）
    bool enable_debug_mode;                // 是否启用调试模式
} GlobalConfig;

/**
 * 🔺 三角合球配置结构
 * 三角形阵型的球体合并参数
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int formation_angle;                   // 阵型角度（度）
    int initial_delay;                     // 初始延迟（毫秒）
    int split_delay;                       // 分身延迟（毫秒）
    int final_split_delay;                 // 最终分身延迟（毫秒）
    bool enable_ball_ejection;             // 是否启用吐球
    bool use_arrow_direction;              // 是否使用箭头方向
    int triangle_radius;                   // 三角形半径
    int correction_offset;                 // 修正偏移量
} TriangleMergingConfig;

/**
 * ⭕ 中分配置结构
 * 中心分割合球参数
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int initial_delay;                     // 初始延迟（毫秒）
    int split_delay;                       // 分身延迟（毫秒）
    int final_split_delay;                 // 最终分身延迟（毫秒）
    bool enable_ball_ejection;             // 是否启用吐球
    bool use_vertical_split;               // 是否使用垂直分割
    int correction_value;                  // 修正值
    int position_offset;                   // 位置偏移
} CenterSplitConfig;

/**
 * ➕ 四分配置结构
 * 四分割合球参数
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int first_split_delay;                 // 第一次分身延迟（毫秒）
    int second_split_delay;                // 第二次分身延迟（毫秒）
    int final_split_delay;                 // 最终分身延迟（毫秒）
    bool enable_ball_ejection;             // 是否启用吐球
    int preset_mode;                       // 预设模式（0=快速，1=精确，2=平衡）
    bool enable_validation;                // 是否启用参数验证
} QuarterSplitConfig;

/**
 * ↩️ 后仰配置结构
 * 后仰操作参数
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int lean_angle;                        // 后仰角度（度）
    int lean_distance;                     // 后仰距离
    int execution_delay;                   // 执行延迟（毫秒）
    bool use_dynamic_angle;                // 是否使用动态角度
    int direction_correction;              // 方向修正值
} BackwardLeanConfig;

/**
 * 🎛️ 杠杆宏配置结构
 * 杠杆宏操作参数
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int double_click_threshold;            // 双击检测阈值（毫秒）
    int execution_delay;                   // 执行延迟（毫秒）
    bool enable_partial_execution;         // 是否启用部分执行
    int time_window;                       // 时间窗口（毫秒）
    bool enable_state_management;          // 是否启用状态管理
} LeverMacroConfig;

/**
 * 🔄 旋转配置结构
 * 各种旋转操作参数（半旋/全旋/蛇手）
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int rotation_angle;                    // 旋转角度（度）
    int rotation_radius;                   // 旋转半径
    int step_delay;                        // 步骤延迟（毫秒）
    int rotation_steps;                    // 旋转步数
    bool enable_ball_ejection;             // 是否启用吐球
    int direction_mode;                    // 方向模式（0=自动，1=顺时针，2=逆时针）
    int radius_scaling[4];                 // 半径缩放数组（4步）
} RotationConfig;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🤖 宏操作配置结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 分割宏配置结构
 * 用于各种分割宏（二分/四分/八分/十六分宏）
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    int split_count;                       // 分身次数
    int delay_between_splits;              // 分身间隔（毫秒）
    int initial_delay;                     // 初始延迟（毫秒）
    bool enable_validation;                // 是否启用验证
} SplitMacroConfig;

/**
 * 连续动作配置结构
 * 用于长按宏和连击宏
 */
typedef struct {
    bool is_enabled;                       // 是否启用
    BYTE trigger_key;                      // 触发按键
    BYTE action_key;                       // 动作按键
    int action_interval;                   // 动作间隔（毫秒）
    int max_duration;                      // 最大持续时间（毫秒）
    bool is_continuous;                    // 是否连续执行
    int repeat_count;                      // 重复次数（非连续模式）
} ContinuousActionConfig;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📋 主配置结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 🎮 球体合并主配置结构
 * 包含所有子配置的主配置对象
 */
typedef struct {
    // 基础操作配置
    GlobalConfig global;                   // 全局配置
    TriangleMergingConfig triangle_1;      // 三角合球1配置
    TriangleMergingConfig triangle_2;      // 三角合球2配置
    CenterSplitConfig center_split;        // 中分配置
    QuarterSplitConfig quarter_split;      // 四分配置
    BackwardLeanConfig backward_lean;      // 后仰配置
    LeverMacroConfig lever_macro;          // 杠杆宏配置
    
    // 旋转操作配置
    RotationConfig half_rotation;          // 半旋配置
    RotationConfig full_rotation;          // 全旋配置
    RotationConfig snake_hand;             // 蛇手配置
    
    // 宏操作配置
    SplitMacroConfig split_macro_2;        // 二分宏配置
    SplitMacroConfig split_macro_4;        // 四分宏配置
    SplitMacroConfig split_macro_8;        // 八分宏配置
    SplitMacroConfig split_macro_16;       // 十六分宏配置
    SplitMacroConfig origin_macro;         // 原地宏配置
    
    // 连续动作配置
    ContinuousActionConfig continuous_1;   // 长按宏1配置
    ContinuousActionConfig continuous_2;   // 长按宏2配置
    ContinuousActionConfig combo_1;        // 连击宏1配置
    ContinuousActionConfig combo_2;        // 连击宏2配置
    
    // 配置元信息
    char config_version[16];               // 配置版本
    uint64_t last_modified;                // 最后修改时间
    bool is_loaded;                        // 是否已加载
} BallMergingConfiguration;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 配置管理函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 初始化配置为默认值
 * @param config 配置结构指针
 * @return 是否初始化成功
 */
bool config_init_defaults(BallMergingConfiguration* config);

/**
 * 从文件加载配置
 * @param config 配置结构指针
 * @param filename 配置文件名（NULL使用默认文件名）
 * @return 是否加载成功
 */
bool config_load_from_file(BallMergingConfiguration* config, const char* filename);

/**
 * 保存配置到文件
 * @param config 配置结构指针
 * @param filename 配置文件名（NULL使用默认文件名）
 * @return 是否保存成功
 */
bool config_save_to_file(const BallMergingConfiguration* config, const char* filename);

/**
 * 验证配置参数有效性
 * @param config 配置结构指针
 * @return 是否验证通过
 */
bool config_validate(const BallMergingConfiguration* config);

/**
 * 重置配置到默认值
 * @param config 配置结构指针
 * @return 是否重置成功
 */
bool config_reset_to_defaults(BallMergingConfiguration* config);

/**
 * 获取配置状态信息
 * @param config 配置结构指针
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 是否获取成功
 */
bool config_get_status_info(const BallMergingConfiguration* config, char* buffer, size_t buffer_size);

#endif // CONFIG_H
