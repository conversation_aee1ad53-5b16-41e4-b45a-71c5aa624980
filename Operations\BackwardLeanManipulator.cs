/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * ↩️ 后仰操作类 (BackwardLeanManipulator.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现后仰球体移动操作，这是一种特殊的球体控制技术：                                    │
 * │                                                                                     │
 * │ 🎯 核心功能:                                                                        │
 * │   • 后仰计算 - 根据移动方向计算反向后仰位置                                          │
 * │   • 角度控制 - 可配置的后仰角度，影响后仰幅度                                        │
 * │   • 动态优化 - 根据方向向量动态调整后仰角度                                          │
 * │   • 精确定位 - 使用复杂的几何计算确保精确的后仰位置                                  │
 * │                                                                                     │
 * │ 🔄 操作流程:                                                                        │
 * │   1. 初始设置 - 释放并重新按下鼠标，等待初始延迟                                     │
 * │   2. 第一分身 - 在当前位置执行第一次分身                                             │
 * │   3. 后仰移动 - 计算并移动到后仰位置                                                 │
 * │   4. 第二分身 - 在后仰位置执行第二次分身                                             │
 * │   5. 最终移动 - 移动到最终目标位置                                                   │
 * │   6. 完成序列 - 释放鼠标并执行最终分身                                               │
 * │                                                                                     │
 * │ 🧮 数学原理:                                                                        │
 * │   • 后仰X坐标 = 中心X - (移动X符号) × |方向Y| × 后仰角度                             │
 * │   • 后仰Y坐标 = 中心Y - (移动Y符号) × |方向X| × 后仰角度                             │
 * │   • 使用垂直向量计算确保后仰方向正确                                                 │
 * │   • 支持动态角度优化，根据向量模长调整                                               │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 几何精确 - 使用精确的几何计算确定后仰位置
 * • 自适应角度 - 可根据移动距离动态调整后仰角度
 * • 参数验证 - 完善的参数有效性检查
 * • 错误恢复 - 健壮的错误处理机制
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 几何计算和输入控制
 * • 使用 BackwardLeanConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局设置
 *
 * 💡 使用技巧:
 * • 后仰角度建议设置在20-60度之间
 * • 在球体密集区域效果更明显
 * • 可以配合其他操作形成组合技
 * • 动态角度优化可以提高适应性
 *
 * ⚠️ 注意事项:
 * • 后仰角度过大可能导致位置偏移
 * • 需要足够的操作空间进行后仰
 * • 方向向量不能为零向量
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 后仰操作类 - 处理后仰球体移动的功能
    /// 这个类实现了后仰操作的完整流程
    /// </summary>
    public class BackwardLeanManipulator
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.BackwardLeanConfig _config;       // 后仰配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;      // 全局配置

        /// <summary>
        /// 构造函数 - 初始化后仰操作器
        /// </summary>
        /// <param name="config">后仰配置</param>
        /// <param name="globalConfig">全局配置</param>
        public BackwardLeanManipulator(BallMergingConfiguration.BackwardLeanConfig config, 
                                      BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
        }

        /// <summary>
        /// 执行后仰操作的主函数
        /// 这是后仰功能的入口点
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形区域</param>
        /// <param name="directionVector">移动方向向量</param>
        /// <returns>操作是否成功</returns>
        public bool ExecuteBackwardLean(Struct.POINT mousePosition, Struct.RECT windowRect, 
                                       (double x, double y) directionVector)
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 第一步：计算缩放后的半径和修正值
                // 根据窗口大小调整操作半径，确保在不同分辨率下效果一致
                double scaledRadius = BallManipulationCore.ScaleRadius(
                    _config.MovementRadius,                    // 基础移动半径
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 计算位置修正值，用于精确定位（后仰使用56作为基础修正值）
                int correctionValue = BallManipulationCore.ScaleCorrectionValue(
                    56,                                        // 基础修正值
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 计算缩放后的后仰角度
                int leanAngleScaled = BallManipulationCore.ScaleCorrectionValue(
                    _config.LeanAngle,                         // 基础后仰角度
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 第二步：计算移动方向
                var (dx, dy, slope) = BallManipulationCore.CalculateMouseDirection(mousePosition, windowRect);
                var (sx3, sy3) = BallManipulationCore.CalculateMovementParameters(scaledRadius, slope, dx);

                // 第三步：计算方向向量分量
                var (dwX, dwY) = BallManipulationCore.CalculateDirectionVector(directionVector.x, directionVector.y / directionVector.x);

                // 第四步：执行后仰序列
                return ExecuteBackwardLeanSequence(mousePosition, (sx3, sy3), (dwX, dwY), correctionValue, leanAngleScaled);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"后仰操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行完整的后仰序列
        /// 这是后仰操作的核心执行逻辑
        /// </summary>
        /// <param name="centerPosition">操作中心位置</param>
        /// <param name="movementVector">移动向量 (sx3, sy3)</param>
        /// <param name="directionVector">方向向量 (dwX, dwY)</param>
        /// <param name="correctionValue">位置修正值</param>
        /// <param name="leanAngle">缩放后的后仰角度</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteBackwardLeanSequence(Struct.POINT centerPosition, 
                                                (double sx3, double sy3) movementVector,
                                                (double dwX, double dwY) directionVector,
                                                int correctionValue,
                                                int leanAngle)
        {
            try
            {
                // 第一阶段：初始设置
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                Thread.Sleep(20);                                  // 短暂等待
                BallManipulationCore.SimulateMouseButton(true);    // 按下鼠标左键
                Thread.Sleep(_config.InitialSplitDelay);           // 等待初始分身延迟

                // 第二阶段：第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.MouseDownDelay);

                // 第三阶段：计算后仰位置
                // 后仰的核心是向相反方向移动一定角度
                int backwardX = CalculateBackwardLeanX(centerPosition, movementVector, directionVector, leanAngle);
                int backwardY = CalculateBackwardLeanY(centerPosition, movementVector, directionVector, leanAngle);

                // 移动到后仰位置
                BallManipulationCore.SetCursorPosition(backwardX, backwardY);
                Thread.Sleep(_config.FirstDragDelay);             // 等待第一次拖拽延迟

                // 第四阶段：第二次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.SecondSplitDelay);

                // 第五阶段：移动到最终位置
                // 计算最终目标位置
                int finalX = (int)(centerPosition.X + movementVector.sx3 - directionVector.dwX * correctionValue);
                int finalY = (int)(centerPosition.Y + movementVector.sy3 - directionVector.dwY * correctionValue);

                // 移动到最终位置
                BallManipulationCore.SetCursorPosition(finalX, finalY);
                Thread.Sleep(_config.SecondDragDelay);            // 等待第二次拖拽延迟

                // 第六阶段：释放鼠标并完成序列
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键

                // 第七阶段：处理吐球功能（如果启用）
                if (_config.EnableBallEjection)
                {
                    ExecuteBallEjection();  // 执行吐球序列
                }

                // 第八阶段：执行最终分身序列
                ExecuteFinalSplitSequence();

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"后仰序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 计算后仰位置的X坐标
        /// 根据移动向量和方向向量计算后仰的X位置
        /// </summary>
        /// <param name="centerPosition">中心位置</param>
        /// <param name="movementVector">移动向量</param>
        /// <param name="directionVector">方向向量</param>
        /// <param name="leanAngle">后仰角度</param>
        /// <returns>后仰位置的X坐标</returns>
        private int CalculateBackwardLeanX(Struct.POINT centerPosition, 
                                          (double sx3, double sy3) movementVector,
                                          (double dwX, double dwY) directionVector,
                                          int leanAngle)
        {
            // 计算后仰的X坐标
            // 使用移动向量的符号和方向向量的Y分量来确定后仰方向
            double leanX = centerPosition.X - (movementVector.sx3 / Math.Abs(movementVector.sx3)) * Math.Abs(directionVector.dwY) * leanAngle;
            return (int)leanX;  // 转换为整数坐标
        }

        /// <summary>
        /// 计算后仰位置的Y坐标
        /// 根据移动向量和方向向量计算后仰的Y位置
        /// </summary>
        /// <param name="centerPosition">中心位置</param>
        /// <param name="movementVector">移动向量</param>
        /// <param name="directionVector">方向向量</param>
        /// <param name="leanAngle">后仰角度</param>
        /// <returns>后仰位置的Y坐标</returns>
        private int CalculateBackwardLeanY(Struct.POINT centerPosition, 
                                          (double sx3, double sy3) movementVector,
                                          (double dwX, double dwY) directionVector,
                                          int leanAngle)
        {
            // 计算后仰的Y坐标
            // 使用移动向量的符号和方向向量的X分量来确定后仰方向
            double leanY = centerPosition.Y - (movementVector.sy3 / Math.Abs(movementVector.sy3)) * Math.Abs(directionVector.dwX) * leanAngle;
            return (int)leanY;  // 转换为整数坐标
        }

        /// <summary>
        /// 执行吐球序列
        /// 启动吐球功能的按键操作
        /// </summary>
        private void ExecuteBallEjection()
        {
            // 获取吐球按键的扫描码
            uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
            // 按下吐球按键（不立即释放，在最终序列中释放）
            API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, 0, 0);
        }

        /// <summary>
        /// 执行最终分身序列
        /// 进行多次分身操作以完成合球
        /// </summary>
        private void ExecuteFinalSplitSequence()
        {
            // 执行20次分身操作，确保球体完全合并
            for (int i = 0; i < 20; i++)
            {
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FinalSplitDelay);
            }

            // 如果启用了吐球功能，完成吐球操作
            if (_config.EnableBallEjection)
            {
                // 获取吐球按键的扫描码
                uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
                // 释放吐球按键
                API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
            }
        }

        /// <summary>
        /// 检查是否可以执行后仰操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">当前按下的按键</param>
        /// <param name="windowHandle">当前前台窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改后仰的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.BackwardLeanConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                      // 更新启用状态
            _config.EnableBallEjection = newConfig.EnableBallEjection;    // 更新吐球设置
            _config.TriggerKey = newConfig.TriggerKey;                    // 更新触发按键
            _config.MovementRadius = newConfig.MovementRadius;            // 更新移动半径
            _config.LeanAngle = newConfig.LeanAngle;                      // 更新后仰角度
            _config.InitialSplitDelay = newConfig.InitialSplitDelay;      // 更新初始分身延迟
            _config.MouseDownDelay = newConfig.MouseDownDelay;            // 更新鼠标按下延迟
            _config.FirstDragDelay = newConfig.FirstDragDelay;            // 更新第一次拖拽延迟
            _config.SecondSplitDelay = newConfig.SecondSplitDelay;        // 更新第二次分身延迟
            _config.SecondDragDelay = newConfig.SecondDragDelay;          // 更新第二次拖拽延迟
            _config.FinalSplitDelay = newConfig.FinalSplitDelay;          // 更新最终分身延迟
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的后仰配置信息
        /// </summary>
        /// <returns>当前后仰配置</returns>
        public BallMergingConfiguration.BackwardLeanConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }

        /// <summary>
        /// 根据移动方向计算最优后仰角度
        /// 动态调整后仰角度以获得最佳效果
        /// </summary>
        /// <param name="directionVector">移动方向向量</param>
        /// <param name="baseAngle">基础后仰角度</param>
        /// <returns>优化后的后仰角度</returns>
        public int CalculateOptimalLeanAngle((double x, double y) directionVector, int baseAngle)
        {
            // 计算方向向量的模长
            double magnitude = Math.Sqrt(directionVector.x * directionVector.x + directionVector.y * directionVector.y);
            
            // 根据模长计算调整因子（限制在0.5到2.0之间）
            double factor = Math.Min(magnitude / 100.0, 2.0);
            factor = Math.Max(factor, 0.5);
            
            // 返回调整后的角度
            return (int)(baseAngle * factor);
        }

        /// <summary>
        /// 验证后仰参数
        /// 在执行前检查参数是否有效
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <param name="directionVector">方向向量</param>
        /// <returns>参数是否有效</returns>
        public bool ValidateParameters(Struct.POINT mousePosition, Struct.RECT windowRect, (double x, double y) directionVector)
        {
            // 检查鼠标是否在有效的窗口范围内
            if (mousePosition.X < windowRect.left || mousePosition.X > windowRect.right ||
                mousePosition.Y < windowRect.top || mousePosition.Y > windowRect.bottom)
            {
                return false;  // 鼠标位置无效
            }

            // 检查方向向量是否有效（不能太小）
            if (Math.Abs(directionVector.x) < 0.001 && Math.Abs(directionVector.y) < 0.001)
            {
                return false;  // 方向向量太小，无效
            }

            // 检查后仰角度是否合理
            if (_config.LeanAngle < 0 || _config.LeanAngle > 180)
            {
                return false;  // 角度值不合理
            }

            return true;  // 所有参数都有效
        }
    }
}
