/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 中分合球操作模块头文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 中分合球操作的头文件定义
 * • 基于C#版本CenterSplitMerger的完整移植和优化
 * • 实现从中心位置进行球体分割合并（原地合球）
 * 
 * 🎯 功能特点:
 * • ⭕ 中心分割合球 - 从中心位置进行精确分割
 * • 📐 垂直分割模式 - 支持垂直方向的分割操作
 * • 🎯 精确位置修正 - 智能的位置修正算法
 * • 💫 三阶段操作序列 - 准备、分割、合并三个阶段
 * • 🔄 自适应参数调整 - 根据游戏状态动态调整
 * 
 * 🔗 算法原理:
 * • 第一步：计算中心位置和修正参数
 * • 第二步：执行垂直或水平分割
 * • 第三步：进行位置微调和修正
 * • 第四步：执行最终合并序列
 * • 第五步：处理吐球功能（如果启用）
 * 
 * ⚠️ 使用注意:
 * • 适合在球体密集区域使用
 * • 垂直分割模式适合特殊场景
 * • 可以通过调整修正值优化精度
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#ifndef CENTER_SPLIT_H
#define CENTER_SPLIT_H

#include <stdbool.h>
#include <stdint.h>
#include "../core/ball_core.h"
#include "../config/config.h"

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 中分合球常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define CENTER_SPLIT_MAX_CORRECTION 100   // 最大修正值
#define CENTER_SPLIT_MIN_OFFSET -50       // 最小位置偏移
#define CENTER_SPLIT_MAX_OFFSET 50        // 最大位置偏移
#define CENTER_SPLIT_FINAL_COUNT 20       // 最终分身次数
#define CENTER_SPLIT_TIMEOUT 10000        // 操作超时时间（毫秒）

// 中分合球执行状态
typedef enum {
    CENTER_STATE_IDLE = 0,                // 空闲状态
    CENTER_STATE_PREPARING,               // 准备阶段
    CENTER_STATE_CALCULATING,             // 计算阶段
    CENTER_STATE_POSITIONING,             // 定位阶段
    CENTER_STATE_SPLITTING,               // 分割阶段
    CENTER_STATE_CORRECTING,              // 修正阶段
    CENTER_STATE_FINALIZING,              // 完成阶段
    CENTER_STATE_ERROR                    // 错误状态
} CenterSplitExecutionState;

// 分割方向类型
typedef enum {
    CENTER_SPLIT_HORIZONTAL = 0,          // 水平分割
    CENTER_SPLIT_VERTICAL,                // 垂直分割
    CENTER_SPLIT_AUTO                     // 自动选择
} CenterSplitDirection;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 中分合球数据结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * ⭕ 中分合球执行上下文
 * 包含执行过程中的所有状态信息
 */
typedef struct {
    // 配置信息
    CenterSplitConfig config;             // 中分配置
    GlobalConfig global_config;           // 全局配置
    
    // 执行状态
    CenterSplitExecutionState state;      // 当前执行状态
    bool is_executing;                    // 是否正在执行
    uint64_t start_time;                  // 开始时间
    uint64_t last_operation_time;         // 最后操作时间
    
    // 位置信息
    Point mouse_position;                 // 鼠标位置
    Rectangle window_rect;                // 窗口矩形
    Point center_position;                // 中心位置
    Point corrected_position;             // 修正后位置
    
    // 计算参数
    DirectionVector direction_vector;     // 方向向量
    CenterSplitDirection split_direction; // 分割方向
    int correction_applied;               // 已应用的修正值
    int position_offset_applied;          // 已应用的位置偏移
    
    // 执行统计
    int splits_executed;                  // 已执行分身数
    int corrections_applied;              // 已应用修正数
    
    // 错误信息
    char last_error[256];                 // 最后错误信息
    int error_code;                       // 错误代码
} CenterSplitContext;

/**
 * 🎯 中分合球结果结构
 * 包含执行结果和统计信息
 */
typedef struct {
    bool success;                         // 是否执行成功
    uint64_t execution_time;              // 执行时间（毫秒）
    int total_splits;                     // 总分身次数
    int corrections_used;                 // 使用的修正次数
    CenterSplitDirection final_direction; // 最终使用的分割方向
    char result_message[256];             // 结果消息
    int final_error_code;                 // 最终错误代码
} CenterSplitResult;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 中分合球核心函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 初始化中分合球上下文
 * @param context 上下文指针
 * @param center_config 中分配置
 * @param global_config 全局配置
 * @return 是否初始化成功
 */
bool center_split_init_context(CenterSplitContext* context,
                               const CenterSplitConfig* center_config,
                               const GlobalConfig* global_config);

/**
 * 执行中分合球操作
 * @param context 上下文指针
 * @param mouse_pos 鼠标位置
 * @param window_rect 窗口矩形
 * @param result 执行结果输出
 * @return 是否执行成功
 */
bool center_split_execute(CenterSplitContext* context,
                          Point mouse_pos,
                          Rectangle window_rect,
                          CenterSplitResult* result);

/**
 * 停止中分合球操作
 * @param context 上下文指针
 * @return 是否停止成功
 */
bool center_split_stop(CenterSplitContext* context);

/**
 * 重置中分合球上下文
 * @param context 上下文指针
 */
void center_split_reset_context(CenterSplitContext* context);

/**
 * 销毁中分合球上下文
 * @param context 上下文指针
 */
void center_split_destroy_context(CenterSplitContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 中分合球计算函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算中心位置
 * @param context 上下文指针
 * @return 是否计算成功
 */
bool center_split_calculate_center_position(CenterSplitContext* context);

/**
 * 计算分割方向
 * @param context 上下文指针
 * @return 是否计算成功
 */
bool center_split_calculate_split_direction(CenterSplitContext* context);

/**
 * 应用位置修正
 * @param context 上下文指针
 * @return 是否修正成功
 */
bool center_split_apply_position_correction(CenterSplitContext* context);

/**
 * 验证中分参数
 * @param context 上下文指针
 * @return 是否验证通过
 */
bool center_split_validate_parameters(CenterSplitContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 中分合球执行函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行准备阶段
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_preparation(CenterSplitContext* context);

/**
 * 执行定位操作
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_positioning(CenterSplitContext* context);

/**
 * 执行分割操作
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_split_operation(CenterSplitContext* context);

/**
 * 执行位置修正
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_position_correction(CenterSplitContext* context);

/**
 * 执行最终分身序列
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_final_split_sequence(CenterSplitContext* context);

/**
 * 执行吐球操作
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool center_split_execute_ball_ejection(CenterSplitContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 中分合球工具函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 获取当前执行状态
 * @param context 上下文指针
 * @return 当前执行状态
 */
CenterSplitExecutionState center_split_get_state(const CenterSplitContext* context);

/**
 * 检查是否正在执行
 * @param context 上下文指针
 * @return 是否正在执行
 */
bool center_split_is_executing(const CenterSplitContext* context);

/**
 * 获取执行进度百分比
 * @param context 上下文指针
 * @return 进度百分比（0-100）
 */
int center_split_get_progress(const CenterSplitContext* context);

/**
 * 获取状态信息字符串
 * @param context 上下文指针
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 是否获取成功
 */
bool center_split_get_status_info(const CenterSplitContext* context,
                                  char* buffer,
                                  size_t buffer_size);

/**
 * 设置错误信息
 * @param context 上下文指针
 * @param error_code 错误代码
 * @param error_message 错误消息
 */
void center_split_set_error(CenterSplitContext* context,
                            int error_code,
                            const char* error_message);

/**
 * 获取最后错误信息
 * @param context 上下文指针
 * @param error_code 错误代码输出
 * @param error_message 错误消息输出
 * @param message_size 消息缓冲区大小
 * @return 是否有错误
 */
bool center_split_get_last_error(const CenterSplitContext* context,
                                 int* error_code,
                                 char* error_message,
                                 size_t message_size);

#endif // CENTER_SPLIT_H
