/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🔄 旋转操作类 (RotationManipulator.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现基于旋转的球体操作，支持多种旋转模式和复杂的移动模式：                            │
 * │                                                                                     │
 * │ 🎯 支持的旋转类型:                                                                  │
 * │   • 半旋 (HalfRotation) - 180度旋转操作                                             │
 * │   • 全旋 (FullRotation) - 360度完整旋转                                             │
 * │   • 蛇手 (SnakeHand) - 蛇形移动模式                                                 │
 * │                                                                                     │
 * │ 🔄 4步旋转序列:                                                                     │
 * │   每种旋转类型都包含4个步骤，每步都有独立的配置：                                     │
 * │   • 步骤1: 角度1 + 半径1 + 时间延迟1                                                │
 * │   • 步骤2: 角度2 + 半径2 + 时间延迟2                                                │
 * │   • 步骤3: 角度3 + 半径3 + 时间延迟3                                                │
 * │   • 步骤4: 角度4 + 半径4 + 时间延迟4                                                │
 * │                                                                                     │
 * │ 🧮 旋转计算:                                                                        │
 * │   • 方向检测 - 根据鼠标位置和方向向量确定旋转方向                                    │
 * │   • 角度累积 - 每步旋转后累积当前角度                                                │
 * │   • 向量变换 - 使用三角函数计算新的方向向量                                          │
 * │   • 位置计算 - 根据半径和角度计算具体移动位置                                        │
 * │                                                                                     │
 * │ ⚙️ 高级功能:                                                                        │
 * │   • 分辨率适配 - 自动缩放半径适应不同分辨率                                          │
 * │   • 方向智能 - 自动判断顺时针或逆时针旋转                                            │
 * │   • 吐球集成 - 可选的吐球功能集成                                                   │
 * │   • 精确控制 - 每步独立的时间和空间控制                                              │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 模块化设计 - 每个旋转步骤独立可配置
 * • 数学精确 - 使用精确的三角函数计算
 * • 类型安全 - 强类型的旋转类型枚举
 * • 高度可配置 - 支持细粒度的参数调整
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 角度计算和输入控制
 * • 使用 RotationConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局设置
 * • 使用 RotationType 枚举 - 旋转类型定义
 *
 * 💡 使用技巧:
 * • 角度建议设置在30-60度之间
 * • 半径可根据操作需求调整（60-120像素）
 * • 时间延迟可根据网络状况优化
 * • 不同旋转类型适合不同的游戏场景
 *
 * ⚠️ 注意事项:
 * • 旋转过程中避免手动移动鼠标
 * • 确保有足够的操作空间
 * • 注意旋转方向的正确性
 * • 复杂旋转可能需要更多时间
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 旋转操作类 - 处理基于旋转的球体操作（半旋/旋转/蛇手）
    /// 这个类实现了各种旋转模式和移动模式的完整操作流程
    /// </summary>
    public class RotationManipulator
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.RotationConfig _config;       // 旋转配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;  // 全局配置
        private double _currentAngle = 0;  // 当前旋转角度

        /// <summary>
        /// 构造函数 - 初始化旋转操作器
        /// </summary>
        /// <param name="config">旋转配置</param>
        /// <param name="globalConfig">全局配置</param>
        public RotationManipulator(BallMergingConfiguration.RotationConfig config, 
                                  BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
        }

        /// <summary>
        /// 执行旋转操作的主函数
        /// 这是旋转功能的入口点
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形区域</param>
        /// <param name="directionVector">移动方向向量</param>
        /// <param name="rotationType">旋转类型</param>
        /// <returns>操作是否成功</returns>
        public bool ExecuteRotation(Struct.POINT mousePosition, Struct.RECT windowRect, 
                                   (double x, double y) directionVector, RotationType rotationType)
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 第一步：计算移动方向和参数
                var (dx, dy, slope) = BallManipulationCore.CalculateMouseDirection(mousePosition, windowRect);
                var (dwX, dwY) = BallManipulationCore.CalculateDirectionVector(directionVector.x, directionVector.y / directionVector.x);

                // 第二步：确定旋转方向
                // 根据鼠标位置和方向向量确定是顺时针还是逆时针旋转
                int direction = DetermineRotationDirection(dx, dy, directionVector);

                // 第三步：执行旋转序列
                return ExecuteRotationSequence(mousePosition, windowRect, (dwX, dwY), direction, rotationType);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"旋转操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 确定旋转方向
        /// 根据鼠标位置和方向向量计算旋转方向
        /// </summary>
        /// <param name="dx">X方向分量</param>
        /// <param name="dy">Y方向分量</param>
        /// <param name="directionVector">方向向量</param>
        /// <returns>方向乘数（1或-1）</returns>
        private int DetermineRotationDirection(double dx, double dy, (double x, double y) directionVector)
        {
            // 计算方向向量的斜率
            double eqt_k = directionVector.y / directionVector.x;
            
            // 根据几何关系确定旋转方向
            bool condition = (((dx * -1 * eqt_k > -dy) && directionVector.x > 0) || 
                             ((dx * -1 * eqt_k < -dy) && directionVector.x < 0));
            
            // 返回方向乘数
            return (int)(Math.Abs(directionVector.x) / directionVector.x);
        }

        /// <summary>
        /// 执行完整的旋转序列
        /// 这是旋转操作的核心执行逻辑
        /// </summary>
        /// <param name="centerPosition">旋转中心位置</param>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <param name="directionVector">方向向量 (dwX, dwY)</param>
        /// <param name="direction">旋转方向</param>
        /// <param name="rotationType">旋转类型</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteRotationSequence(Struct.POINT centerPosition, Struct.RECT windowRect,
                                            (double dwX, double dwY) directionVector, int direction,
                                            RotationType rotationType)
        {
            try
            {
                // 第一步：缩放半径
                // 根据窗口大小缩放所有半径值
                int[] scaledRadii = ScaleRadii(windowRect);
                
                // 第二步：初始化旋转
                _currentAngle = 0;  // 重置当前角度
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                BallManipulationCore.SimulateMouseButton(true);   // 按下鼠标左键
                Thread.Sleep(20);                                 // 短暂等待
                BallManipulationCore.SetCursorPosition(centerPosition.X, centerPosition.Y);  // 移动到中心
                Thread.Sleep(_config.TimingDelays[0]);           // 等待初始延迟

                // 第三步：设置初始角度
                // 根据方向向量设置起始角度
                _currentAngle += Math.Atan(directionVector.dwY / directionVector.dwX) * 180 / BallManipulationCore.PI;

                // 第四步：执行4步旋转序列
                ExecuteRotationStep(centerPosition, 1, scaledRadii[0], direction, rotationType);  // 第1步
                ExecuteRotationStep(centerPosition, 2, scaledRadii[1], direction, rotationType);  // 第2步
                ExecuteRotationStep(centerPosition, 3, scaledRadii[2], direction, rotationType);  // 第3步
                ExecuteRotationStep(centerPosition, 4, scaledRadii[3], direction, rotationType);  // 第4步

                // 第五步：完成序列
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键

                // 第六步：处理吐球功能（如果启用）
                if (_config.EnableBallEjection)
                {
                    ExecuteBallEjection();  // 执行吐球序列
                }

                // 第七步：执行最终分身序列
                ExecuteFinalSplitSequence();

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"旋转序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行单个旋转步骤
        /// 执行旋转序列中的一个步骤
        /// </summary>
        /// <param name="centerPosition">中心位置</param>
        /// <param name="step">步骤编号（1-4）</param>
        /// <param name="radius">此步骤的半径</param>
        /// <param name="direction">旋转方向</param>
        /// <param name="rotationType">旋转类型</param>
        private void ExecuteRotationStep(Struct.POINT centerPosition, int step, int radius, int direction, RotationType rotationType)
        {
            // 第一步：获取此步骤的角度
            int angle = GetAngleForStep(step);
            
            // 第二步：根据类型和方向应用旋转
            bool isPositive = ShouldUsePositiveRotation(step, direction, rotationType);
            var (dwX, dwY) = BallManipulationCore.PerformAngleRotation(_currentAngle, angle, isPositive);
            
            // 第三步：更新当前角度
            _currentAngle = isPositive ? _currentAngle + angle : _currentAngle - angle;

            // 第四步：计算新位置
            int newX = (int)(centerPosition.X + radius * dwX * direction);
            int newY = (int)(centerPosition.Y + radius * dwY * direction);

            // 第五步：移动到新位置
            BallManipulationCore.SetCursorPosition(newX, newY);
            Thread.Sleep(_config.TimingDelays[step * 2 - 1]);  // 等待移动延迟

            // 第六步：执行分身
            BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.TimingDelays[step * 2]);
        }

        /// <summary>
        /// 获取指定步骤的角度
        /// 根据步骤编号返回对应的角度值
        /// </summary>
        /// <param name="step">步骤编号（1-4）</param>
        /// <returns>该步骤的角度</returns>
        private int GetAngleForStep(int step)
        {
            // 根据步骤编号返回对应的角度
            return step switch
            {
                1 => _config.Angle1,  // 第1步角度
                2 => _config.Angle2,  // 第2步角度
                3 => _config.Angle3,  // 第3步角度
                4 => _config.Angle4,  // 第4步角度
                _ => 45               // 默认45度
            };
        }

        /// <summary>
        /// 确定是否使用正向旋转
        /// 根据步骤、方向和旋转类型确定旋转方向
        /// </summary>
        /// <param name="step">步骤编号</param>
        /// <param name="direction">总体方向</param>
        /// <param name="rotationType">旋转类型</param>
        /// <returns>是否使用正向旋转</returns>
        private bool ShouldUsePositiveRotation(int step, int direction, RotationType rotationType)
        {
            // 根据旋转类型确定旋转方向
            return rotationType switch
            {
                RotationType.HalfRotation => step == 1 ? direction > 0 : direction < 0,  // 半旋模式
                RotationType.FullRotation => step == 1 ? direction > 0 : direction < 0,  // 全旋模式
                RotationType.SnakeHand => step == 1 ? direction > 0 : direction < 0,     // 蛇手模式
                _ => step % 2 == 1  // 默认：奇数步正向，偶数步反向
            };
        }

        /// <summary>
        /// 根据窗口大小缩放半径
        /// 确保在不同分辨率下效果一致
        /// </summary>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <returns>缩放后的半径数组</returns>
        private int[] ScaleRadii(Struct.RECT windowRect)
        {
            // 计算窗口宽度
            int windowWidth = windowRect.right - windowRect.left;
            
            // 缩放所有半径值
            return new int[]
            {
                BallManipulationCore.ScaleCorrectionValue(_config.Radius1, windowWidth, _globalConfig.JoystickSensitivity),
                BallManipulationCore.ScaleCorrectionValue(_config.Radius2, windowWidth, _globalConfig.JoystickSensitivity),
                BallManipulationCore.ScaleCorrectionValue(_config.Radius3, windowWidth, _globalConfig.JoystickSensitivity),
                BallManipulationCore.ScaleCorrectionValue(_config.Radius4, windowWidth, _globalConfig.JoystickSensitivity)
            };
        }

        /// <summary>
        /// 执行吐球序列
        /// 启动吐球功能的按键操作
        /// </summary>
        private void ExecuteBallEjection()
        {
            // 获取吐球按键的扫描码
            uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
            // 按下吐球按键（不立即释放，在最终序列中释放）
            API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, 0, 0);
        }

        /// <summary>
        /// 执行最终分身序列
        /// 完成旋转操作的最后步骤
        /// </summary>
        private void ExecuteFinalSplitSequence()
        {
            // 使用最后一个时间延迟进行最终分身
            int finalDelay = _config.TimingDelays[_config.TimingDelays.Length - 1];
            
            // 执行一次分身
            BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, finalDelay);
            
            // 执行20次额外分身以确保完全合并
            for (int i = 0; i < 20; i++)
            {
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, finalDelay);
            }

            // 如果启用了吐球功能，完成吐球操作
            if (_config.EnableBallEjection)
            {
                // 获取吐球按键的扫描码
                uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
                // 释放吐球按键
                API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
            }
        }

        /// <summary>
        /// 检查是否可以执行旋转操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">触发按键</param>
        /// <param name="windowHandle">当前窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改旋转的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.RotationConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                      // 更新启用状态
            _config.EnableBallEjection = newConfig.EnableBallEjection;    // 更新吐球设置
            _config.TriggerKey = newConfig.TriggerKey;                    // 更新触发按键
            _config.Angle1 = newConfig.Angle1;                           // 更新第1步角度
            _config.Angle2 = newConfig.Angle2;                           // 更新第2步角度
            _config.Angle3 = newConfig.Angle3;                           // 更新第3步角度
            _config.Angle4 = newConfig.Angle4;                           // 更新第4步角度
            _config.Radius1 = newConfig.Radius1;                         // 更新第1步半径
            _config.Radius2 = newConfig.Radius2;                         // 更新第2步半径
            _config.Radius3 = newConfig.Radius3;                         // 更新第3步半径
            _config.Radius4 = newConfig.Radius4;                         // 更新第4步半径
            _config.TimingDelays = newConfig.TimingDelays;               // 更新时间延迟数组
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的旋转配置信息
        /// </summary>
        /// <returns>当前旋转配置</returns>
        public BallMergingConfiguration.RotationConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }
    }

    /// <summary>
    /// 旋转操作类型枚举
    /// 定义不同类型的旋转操作
    /// </summary>
    public enum RotationType
    {
        HalfRotation,   // 半旋
        FullRotation,   // 旋转
        SnakeHand       // 蛇手
    }
}
