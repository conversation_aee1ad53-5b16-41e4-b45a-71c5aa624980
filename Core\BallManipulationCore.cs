/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🔧 球体操作核心工具类 (BallManipulationCore.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 这是整个球体合并系统的核心工具类，提供所有球体操作需要的基础功能：                      │
 * │                                                                                     │
 * │ 🧮 数学计算模块:                                                                    │
 * │   • 方向向量计算 - 将鼠标移动转换为标准化方向                                        │
 * │   • 移动参数计算 - 根据半径和斜率计算具体移动距离                                    │
 * │   • 角度旋转计算 - 处理旋转操作的角度变换                                           │
 * │   • 三角阵型计算 - 计算三角合球的顶点位置                                           │
 * │                                                                                     │
 * │ 🖱️ 输入控制模块:                                                                    │
 * │   • 按键模拟 - 模拟键盘按键操作（分身键等）                                         │
 * │   • 鼠标控制 - 控制鼠标移动、按下、释放                                             │
 * │   • 位置获取 - 获取当前鼠标位置和窗口信息                                           │
 * │                                                                                     │
 * │ 📏 缩放适配模块:                                                                    │
 * │   • 分辨率适配 - 根据窗口大小调整操作参数                                           │
 * │   • 灵敏度调节 - 根据摇杆灵敏度缩放数值                                             │
 * │   • 精度修正 - 提供位置修正和精确定位                                               │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 静态类设计 - 所有方法可直接调用，无需实例化
 * • 纯函数设计 - 输入确定则输出确定，便于测试和调试
 * • 高度复用 - 被所有球体操作类共同使用
 * • 性能优化 - 数学计算经过优化，减少重复计算
 *
 * 🔗 依赖关系:
 * • 被 TriangleBallMerger 调用 - 三角合球的数学计算
 * • 被 CenterSplitMerger 调用 - 中分操作的位置计算
 * • 被 QuarterSplitMerger 调用 - 四分操作的参数缩放
 * • 被 BackwardLeanManipulator 调用 - 后仰的角度计算
 * • 被 RotationManipulator 调用 - 旋转的向量变换
 * • 被 LeverMacroProcessor 调用 - 宏操作的按键模拟
 *
 * ⚠️ 重要提示:
 * 这个类是整个系统的基础，修改时需要特别小心，因为会影响所有其他功能模块
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Diagnostics;
using System.Threading;
using Vanara.PInvoke;

namespace ANYE_Balls.BallMerging.Core
{
    /// <summary>
    /// 球体操作核心类 - 提供所有球体操作的基础数学计算和工具函数
    /// 这个类包含了所有球体合并操作需要的基础功能
    /// </summary>
    public static class BallManipulationCore
    {
        #region 常量定义
        public const double PI = 3.14159265358979;           // 圆周率，用于角度计算
        public const int VK_LBUTTON = 0x01;                  // 鼠标左键的虚拟键码
        public const uint KEYEVENTF_KEYUP = 0x02;            // 按键释放事件标志
        public const int MOUSEEVENTF_LEFTUP = 0x0004;        // 鼠标左键释放事件
        public const int MOUSEEVENTF_LEFTDOWN = 0x0002;      // 鼠标左键按下事件
        #endregion

        #region 数学计算函数

        /// <summary>
        /// 计算球体移动的方向向量
        /// 这个函数将鼠标移动转换为标准化的方向向量
        /// </summary>
        /// <param name="eqt_x">X方向的分量</param>
        /// <param name="eqt_k">方向向量的斜率</param>
        /// <returns>返回标准化后的X和Y方向分量</returns>
        public static (double dwX, double dwY) CalculateDirectionVector(double eqt_x, double eqt_k)
        {
            // 计算向量的模长，用于标准化
            double mu = Math.Sqrt(1 + eqt_k * eqt_k);
            
            // 计算标准化后的X分量，保持方向
            double dwX = Math.Abs(eqt_x) / eqt_x / mu;
            
            // 计算标准化后的Y分量，基于斜率
            double dwY = Math.Abs(eqt_x) / eqt_x * eqt_k / mu;
            
            return (dwX, dwY);  // 返回方向向量
        }

        /// <summary>
        /// 根据半径和斜率计算移动参数
        /// 用于确定球体在指定半径下的具体移动距离
        /// </summary>
        /// <param name="radius">移动半径</param>
        /// <param name="slope">移动斜率</param>
        /// <param name="dx">X方向分量</param>
        /// <returns>返回X和Y方向的移动距离</returns>
        public static (double sx3, double sy3) CalculateMovementParameters(double radius, double slope, double dx)
        {
            // 根据半径和斜率计算X方向移动距离
            double sx3 = Math.Sqrt((radius * radius) / (1 + slope * slope)) * dx / Math.Abs(dx);
            
            // 根据X方向距离和斜率计算Y方向移动距离
            double sy3 = sx3 * slope;
            
            return (sx3, sy3);  // 返回移动参数
        }

        /// <summary>
        /// 计算鼠标相对于窗口中心的方向
        /// 用于确定球体应该向哪个方向移动
        /// </summary>
        /// <param name="mousePos">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <returns>返回方向分量和斜率</returns>
        public static (double dx, double dy, double slope) CalculateMouseDirection(Struct.POINT mousePos, Struct.RECT windowRect)
        {
            // 计算鼠标相对于窗口中心的X偏移
            double dx = mousePos.X - (windowRect.left + windowRect.right) / 2;
            
            // 计算鼠标相对于窗口中心的Y偏移
            double dy = mousePos.Y - (windowRect.top + windowRect.bottom) / 2;
            
            // 计算移动方向的斜率
            double slope = dy / dx;
            
            return (dx, dy, slope);  // 返回方向信息
        }

        /// <summary>
        /// 执行基于角度的旋转计算
        /// 用于旋转类操作，如半旋、全旋等
        /// </summary>
        /// <param name="currentAngle">当前角度（度）</param>
        /// <param name="angleChange">角度变化量（度）</param>
        /// <param name="isPositive">是否为正向旋转</param>
        /// <returns>返回新的X和Y方向分量</returns>
        public static (double dwX, double dwY) PerformAngleRotation(double currentAngle, int angleChange, bool isPositive)
        {
            // 根据旋转方向计算新角度
            double newAngle = isPositive ? currentAngle + angleChange : currentAngle - angleChange;
            
            // 将角度转换为弧度并计算X分量
            double dwX = Math.Cos(newAngle * PI / 180);
            
            // 将角度转换为弧度并计算Y分量
            double dwY = Math.Sin(newAngle * PI / 180);
            
            return (dwX, dwY);  // 返回新的方向分量
        }

        /// <summary>
        /// 计算三角形阵型的三个点位置
        /// 用于三角合球功能
        /// </summary>
        /// <param name="centerX">中心点X坐标</param>
        /// <param name="centerY">中心点Y坐标</param>
        /// <param name="radius">阵型半径</param>
        /// <param name="angle">阵型角度（度）</param>
        /// <returns>返回三个三角形顶点的坐标</returns>
        public static ((double x1, double y1), (double x2, double y2), (double x3, double y3)) CalculateTriangleFormation(
            double centerX, double centerY, double radius, double angle)
        {
            // 设置基础移动向量（沿X轴方向）
            double sx3 = radius;
            double sy3 = 0;
            
            // 将角度转换为弧度
            double angleRad = angle * PI / 360;
            
            // 计算第一个三角形顶点（逆时针旋转）
            double sx1 = (sx3 * Math.Cos(angleRad) - sy3 * Math.Sin(angleRad));
            double sy1 = (sy3 * Math.Cos(angleRad) + sx3 * Math.Sin(angleRad));
            
            // 计算第二个三角形顶点（顺时针旋转）
            double sx2 = (sx3 * Math.Cos(angleRad) + sy3 * Math.Sin(angleRad));
            double sy2 = (sy3 * Math.Cos(angleRad) - sx3 * Math.Sin(angleRad));

            // 返回相对于中心点的三个顶点坐标
            return ((centerX + sx1, centerY + sy1), 
                    (centerX + sx2, centerY + sy2), 
                    (centerX + sx3, centerY + sy3));
        }
        #endregion

        #region 输入工具函数

        /// <summary>
        /// 模拟按键操作
        /// 用于执行分身等按键操作
        /// </summary>
        /// <param name="key">虚拟键码</param>
        /// <param name="delay">按键后的延迟时间（毫秒）</param>
        public static void SimulateKeyPress(byte key, int delay)
        {
            // 获取按键的扫描码
            uint scancode = API.MapVirtualKeyW(key, 0);
            
            // 模拟按键按下
            API.keybd_event(key, (byte)scancode, 0, 0);
            
            // 模拟按键释放
            API.keybd_event(key, (byte)scancode, KEYEVENTF_KEYUP, 0);
            
            // 等待指定的延迟时间
            Thread.Sleep(delay);
        }

        /// <summary>
        /// 获取当前鼠标光标位置
        /// </summary>
        /// <returns>返回当前光标位置</returns>
        public static Struct.POINT GetCurrentCursorPosition()
        {
            // 调用Windows API获取光标位置
            API.GetCursorPos(out Struct.POINT position);
            return position;
        }

        /// <summary>
        /// 获取指定窗口的矩形区域
        /// </summary>
        /// <param name="windowHandle">窗口句柄</param>
        /// <returns>返回窗口矩形</returns>
        public static Struct.RECT GetWindowRectangle(IntPtr windowHandle)
        {
            // 调用Windows API获取窗口矩形
            API.GetWindowRect(windowHandle, out Struct.RECT rect);
            return rect;
        }

        /// <summary>
        /// 模拟鼠标按钮事件
        /// 用于控制鼠标的按下和释放
        /// </summary>
        /// <param name="isDown">true表示按下，false表示释放</param>
        public static void SimulateMouseButton(bool isDown)
        {
            if (isDown)
                // 模拟鼠标左键按下
                API.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
            else
                // 模拟鼠标左键释放
                API.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
        }

        /// <summary>
        /// 设置鼠标光标位置
        /// 用于移动鼠标到指定坐标
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        public static void SetCursorPosition(int x, int y)
        {
            // 调用Windows API设置光标位置
            API.SetCursorPos(x, y);
        }
        #endregion

        #region 缩放工具函数

        /// <summary>
        /// 根据窗口大小和摇杆灵敏度缩放半径
        /// 确保在不同分辨率下操作效果一致
        /// </summary>
        /// <param name="baseRadius">基础半径值</param>
        /// <param name="windowWidth">当前窗口宽度</param>
        /// <param name="joystickSensitivity">摇杆灵敏度</param>
        /// <returns>返回缩放后的半径</returns>
        public static double ScaleRadius(double baseRadius, int windowWidth, float joystickSensitivity)
        {
            // 基于1920分辨率进行缩放计算
            return baseRadius * ((float)windowWidth / 1920 * joystickSensitivity / 1.6);
        }

        /// <summary>
        /// 根据窗口大小和摇杆灵敏度缩放修正值
        /// 用于精确定位和修正偏差
        /// </summary>
        /// <param name="baseCorrectionValue">基础修正值</param>
        /// <param name="windowWidth">当前窗口宽度</param>
        /// <param name="joystickSensitivity">摇杆灵敏度</param>
        /// <returns>返回缩放后的修正值</returns>
        public static int ScaleCorrectionValue(int baseCorrectionValue, int windowWidth, float joystickSensitivity)
        {
            // 基于1920分辨率进行缩放计算，返回整数值
            return (int)(baseCorrectionValue * ((float)windowWidth / 1920 * joystickSensitivity / 1.6));
        }
        #endregion
    }
}
