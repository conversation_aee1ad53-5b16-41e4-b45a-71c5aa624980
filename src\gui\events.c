/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 事件处理模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • GUI事件处理的具体实现
 * • 包含按钮点击、滑杆变化、复选框状态等事件处理
 * • 实现界面与配置的双向同步
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "main_window.h"
#include "../operations/triangle_merge.h"
#include "../operations/center_split.h"
#include <stdio.h>
#include <stdlib.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 全局变量
// ═══════════════════════════════════════════════════════════════════════════════════════

static TriangleMergeContext g_triangle_context = {0};  // 三角合球上下文
static CenterSplitContext g_center_context = {0};     // 中分合球上下文
static bool g_contexts_initialized = false;           // 上下文是否已初始化

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 事件处理函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 处理按钮点击事件
 */
void handle_button_click(MainWindowData* window_data, int control_id) {
    if (!window_data) {
        return;
    }
    
    switch (control_id) {
        case ID_BTN_START: {
            if (!window_data->is_running) {
                // 开始运行
                window_data->is_running = true;
                update_interface_state(window_data);
                update_status_bar(window_data, "正在运行...");
                
                // 初始化操作上下文
                if (!g_contexts_initialized) {
                    triangle_merge_init_context(&g_triangle_context,
                                               &window_data->config->triangle_1,
                                               &window_data->config->global);
                    center_split_init_context(&g_center_context,
                                             &window_data->config->center_split,
                                             &window_data->config->global);
                    g_contexts_initialized = true;
                }
                
                // 保存当前配置
                update_config_from_interface(window_data);
            }
            break;
        }
        
        case ID_BTN_STOP: {
            if (window_data->is_running) {
                // 停止运行
                window_data->is_running = false;
                update_interface_state(window_data);
                update_status_bar(window_data, "已停止");
                
                // 停止所有正在执行的操作
                if (g_contexts_initialized) {
                    triangle_merge_stop(&g_triangle_context);
                    center_split_stop(&g_center_context);
                }
            }
            break;
        }
        
        default:
            break;
    }
}

/**
 * 处理滑杆变化事件
 */
void handle_slider_change(MainWindowData* window_data, int control_id, int position) {
    if (!window_data) {
        return;
    }
    
    wchar_t text[32];
    
    switch (control_id) {
        case ID_SLIDER_JOYSTICK: {
            // 摇杆大小滑杆变化
            swprintf_s(text, 32, L"%d", position);
            SetWindowText(window_data->edit_joystick_size, text);
            
            // 更新配置
            window_data->config->global.joystick_sensitivity = position;
            break;
        }
        
        case ID_SLIDER_DELAY: {
            // 延时时间滑杆变化
            swprintf_s(text, 32, L"%d", position);
            SetWindowText(window_data->edit_delay_time, text);
            
            // 更新配置
            window_data->config->global.base_delay = position;
            break;
        }
        
        case ID_SLIDER_ANGLE_1:
        case ID_SLIDER_ANGLE_2:
        case ID_SLIDER_ANGLE_3: {
            // 角度滑杆变化
            int index = (control_id - ID_SLIDER_ANGLE_1) / 3;
            if (index >= 0 && index < 3) {
                swprintf_s(text, 32, L"%d°", position);
                SetWindowText(window_data->edit_angle[index], text);
                
                // 更新配置（这里可以根据需要更新对应的三角合球配置）
                if (index == 0) {
                    window_data->config->triangle_1.formation_angle = position;
                } else if (index == 1) {
                    window_data->config->triangle_2.formation_angle = position;
                }
            }
            break;
        }
        
        default:
            break;
    }
}

/**
 * 处理复选框状态变化
 */
void handle_checkbox_change(MainWindowData* window_data, int control_id, bool is_checked) {
    if (!window_data) {
        return;
    }
    
    switch (control_id) {
        case ID_CHECK_AUTO_MERGE: {
            // 合球时自动吐复选框
            window_data->config->triangle_1.enable_ball_ejection = is_checked;
            window_data->config->triangle_2.enable_ball_ejection = is_checked;
            window_data->config->center_split.enable_ball_ejection = is_checked;
            break;
        }
        
        case ID_CHECK_STRAIGHT_LINE: {
            // 直线模式复选框
            // 这里可以启用/禁用直线模式相关功能
            break;
        }
        
        case ID_CHECK_CANNON: {
            // 大炮模式复选框
            // 这里可以启用/禁用大炮模式相关功能
            break;
        }
        
        case ID_CHECK_SIDE_MERGE_U: {
            // 侧合为U复选框
            // 这里可以启用/禁用侧合为U功能
            break;
        }
        
        case ID_CHECK_SIDE_MERGE_I: {
            // 侧合左I复选框
            // 这里可以启用/禁用侧合左I功能
            break;
        }
        
        case ID_CHECK_SIDE_4: {
            // 侧合4复选框
            // 这里可以启用/禁用侧合4功能
            break;
        }
        
        case ID_CHECK_QUARTER_5: {
            // 四分5复选框
            window_data->config->quarter_split.is_enabled = is_checked;
            break;
        }
        
        case ID_CHECK_ROTATION_6: {
            // 旋转6复选框
            window_data->config->half_rotation.is_enabled = is_checked;
            break;
        }
        
        case ID_CHECK_BACKWARD_C: {
            // 后仰C复选框
            window_data->config->backward_lean.is_enabled = is_checked;
            break;
        }
        
        case ID_CHECK_CENTER_Q: {
            // 中分寸Q复选框
            window_data->config->center_split.is_enabled = is_checked;
            break;
        }
        
        case ID_CHECK_QUARTER_W: {
            // 四分寸W复选框
            window_data->config->quarter_split.is_enabled = is_checked;
            break;
        }
        
        case ID_CHECK_IMMORTAL_E: {
            // 仙人指E复选框
            // 这里可以启用/禁用仙人指功能
            break;
        }
        
        default:
            break;
    }
}

/**
 * 处理下拉框选择变化
 */
void handle_combobox_change(MainWindowData* window_data, int control_id, int selection) {
    if (!window_data) {
        return;
    }
    
    switch (control_id) {
        case ID_COMBO_SPLIT_KEY: {
            // 分身键下拉框变化
            BYTE keys[] = {'E', 'Q', 'W', 'R'};
            if (selection >= 0 && selection < 4) {
                window_data->config->global.split_key = keys[selection];
            }
            break;
        }
        
        case ID_COMBO_EJECT_KEY: {
            // 吐球键下拉框变化
            BYTE keys[] = {'Q', 'E', 'W', 'R'};
            if (selection >= 0 && selection < 4) {
                window_data->config->global.ball_eject_key = keys[selection];
            }
            break;
        }
        
        case ID_COMBO_KEY_1:
        case ID_COMBO_KEY_2:
        case ID_COMBO_KEY_3: {
            // 三角合球按键选择变化
            int index = (control_id - ID_COMBO_KEY_1) / 3;
            BYTE keys[] = {'1', '2', '3'};
            if (index >= 0 && index < 3 && selection >= 0 && selection < 3) {
                if (index == 0) {
                    window_data->config->triangle_1.trigger_key = keys[selection];
                } else if (index == 1) {
                    window_data->config->triangle_2.trigger_key = keys[selection];
                }
            }
            break;
        }
        
        default:
            break;
    }
}

/**
 * 处理文本框内容变化
 */
void handle_edit_change(MainWindowData* window_data, int control_id, const char* text) {
    if (!window_data || !text) {
        return;
    }
    
    int value = atoi(text);
    
    switch (control_id) {
        case ID_EDIT_JOYSTICK_SIZE: {
            // 摇杆大小输入框变化
            if (value >= 1 && value <= 200) {
                window_data->config->global.joystick_sensitivity = value;
                SendMessage(window_data->slider_joystick, TBM_SETPOS, TRUE, value);
            }
            break;
        }
        
        case ID_EDIT_DELAY_TIME: {
            // 延时时间输入框变化
            if (value >= 10 && value <= 1000) {
                window_data->config->global.base_delay = value;
                SendMessage(window_data->slider_delay, TBM_SETPOS, TRUE, value);
            }
            break;
        }
        
        case ID_EDIT_ANGLE_1:
        case ID_EDIT_ANGLE_2:
        case ID_EDIT_ANGLE_3: {
            // 角度输入框变化
            int index = (control_id - ID_EDIT_ANGLE_1) / 3;
            if (index >= 0 && index < 3 && value >= 0 && value <= 360) {
                SendMessage(window_data->slider_angle[index], TBM_SETPOS, TRUE, value);
                
                if (index == 0) {
                    window_data->config->triangle_1.formation_angle = value;
                } else if (index == 1) {
                    window_data->config->triangle_2.formation_angle = value;
                }
            }
            break;
        }
        
        default:
            break;
    }
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 界面更新函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 更新界面状态
 */
void update_interface_state(MainWindowData* window_data) {
    if (!window_data) {
        return;
    }
    
    // 更新按钮状态
    EnableWindow(window_data->btn_start, !window_data->is_running);
    EnableWindow(window_data->btn_stop, window_data->is_running);
    
    // 更新按钮文本
    SetWindowText(window_data->btn_start, window_data->is_running ? L"运行中..." : L"运行");
    
    // 更新状态栏
    if (window_data->status_bar) {
        const char* status = window_data->is_running ? "状态：正在运行" : "状态：已停止";
        SendMessageA(window_data->status_bar, SB_SETTEXT, 0, (LPARAM)status);
    }
}

/**
 * 更新状态栏信息
 */
void update_status_bar(MainWindowData* window_data, const char* message) {
    if (!window_data || !message) {
        return;
    }
    
    strcpy_s(window_data->status_text, sizeof(window_data->status_text), message);
    
    if (window_data->status_bar) {
        SendMessageA(window_data->status_bar, SB_SETTEXT, 0, (LPARAM)message);
    }
}

/**
 * 从配置更新界面
 */
void update_interface_from_config(MainWindowData* window_data) {
    if (!window_data || !window_data->config) {
        return;
    }
    
    BallMergingConfiguration* config = window_data->config;
    
    // 更新摇杆大小
    wchar_t text[32];
    swprintf_s(text, 32, L"%d", config->global.joystick_sensitivity);
    SetWindowText(window_data->edit_joystick_size, text);
    SendMessage(window_data->slider_joystick, TBM_SETPOS, TRUE, config->global.joystick_sensitivity);
    
    // 更新延时时间
    swprintf_s(text, 32, L"%d", config->global.base_delay);
    SetWindowText(window_data->edit_delay_time, text);
    SendMessage(window_data->slider_delay, TBM_SETPOS, TRUE, config->global.base_delay);
    
    // 更新角度设置
    swprintf_s(text, 32, L"%d°", config->triangle_1.formation_angle);
    SetWindowText(window_data->edit_angle[0], text);
    SendMessage(window_data->slider_angle[0], TBM_SETPOS, TRUE, config->triangle_1.formation_angle);
    
    swprintf_s(text, 32, L"%d°", config->triangle_2.formation_angle);
    SetWindowText(window_data->edit_angle[1], text);
    SendMessage(window_data->slider_angle[1], TBM_SETPOS, TRUE, config->triangle_2.formation_angle);
    
    // 更新复选框状态
    SendMessage(window_data->check_auto_merge, BM_SETCHECK, 
               config->triangle_1.enable_ball_ejection ? BST_CHECKED : BST_UNCHECKED, 0);
    
    // 更新下拉框选择
    // 这里可以根据配置中的按键值设置对应的下拉框选择
}

/**
 * 从界面更新配置
 */
void update_config_from_interface(MainWindowData* window_data) {
    if (!window_data || !window_data->config) {
        return;
    }
    
    // 获取界面上的值并更新到配置中
    // 这个函数在用户点击"运行"按钮时调用，确保配置是最新的
    
    // 更新时间戳
    window_data->config->last_modified = (uint64_t)time(NULL);
}
