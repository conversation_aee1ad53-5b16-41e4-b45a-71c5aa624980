/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * ⭕ 中分合球操作类 (CenterSplitMerger.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现从中心位置进行球体分割合并的功能，也称为"原地合球"：                              │
 * │                                                                                     │
 * │ 🎯 核心功能:                                                                        │
 * │   • 中心分割 - 从球体中心位置开始分割                                                │
 * │   • 方向计算 - 根据鼠标拖拽方向确定合球方向                                          │
 * │   • 垂直分割 - 支持垂直于原方向的分割模式                                            │
 * │   • 位置修正 - 精确的位置修正算法                                                   │
 * │                                                                                     │
 * │ 🔄 操作流程:                                                                        │
 * │   1. 初始设置 - 释放并重新按下鼠标                                                   │
 * │   2. 中心重置 - 移动到中心位置并等待                                                 │
 * │   3. 第一分身 - 在中心位置执行第一次分身                                             │
 * │   4. 准备移动 - 重新设置鼠标状态                                                     │
 * │   5. 第二分身 - 执行第二次分身                                                       │
 * │   6. 最终移动 - 移动到计算出的最终位置                                               │
 * │   7. 完成序列 - 释放鼠标并执行最终分身                                               │
 * │                                                                                     │
 * │ ⚙️ 特殊功能:                                                                        │
 * │   • 垂直分割模式 - 可以垂直于拖拽方向进行分割                                        │
 * │   • 智能修正 - 根据方向向量自动修正最终位置                                          │
 * │   • 双模式支持 - 标准方向和垂直方向两种计算模式                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 精确定位 - 使用修正值确保精确的球体定位
 * • 灵活方向 - 支持多种方向计算模式
 * • 稳定执行 - 多阶段操作确保稳定性
 * • 自适应 - 根据窗口大小自动调整参数
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 方向计算和输入控制
 * • 使用 CenterSplitConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局设置
 *
 * 💡 使用技巧:
 * • 适合在球体密集区域使用
 * • 垂直分割模式适合特殊场景
 * • 可以通过调整修正值优化精度
 *
 * ⚠️ 注意事项:
 * • 需要明确的方向向量输入
 * • 执行时避免鼠标移动
 * • 注意与其他操作的时序配合
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 中分合球操作类 - 处理从中心位置进行球体分割合并的功能
    /// 这个类实现了中分（原地合球）的完整操作流程
    /// </summary>
    public class CenterSplitMerger
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.CenterSplitConfig _config;         // 中分配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;       // 全局配置

        /// <summary>
        /// 构造函数 - 初始化中分合球操作器
        /// </summary>
        /// <param name="config">中分配置</param>
        /// <param name="globalConfig">全局配置</param>
        public CenterSplitMerger(BallMergingConfiguration.CenterSplitConfig config, 
                                BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
        }

        /// <summary>
        /// 执行中分合球操作的主函数
        /// 这是中分功能的入口点
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形区域</param>
        /// <param name="directionVector">移动方向向量</param>
        /// <param name="useVerticalSplit">是否使用垂直分割模式</param>
        /// <returns>操作是否成功</returns>
        public bool ExecuteCenterSplit(Struct.POINT mousePosition, Struct.RECT windowRect, 
                                     (double x, double y) directionVector, bool useVerticalSplit = false)
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 第一步：计算缩放后的半径和修正值
                // 根据窗口大小调整操作半径，确保在不同分辨率下效果一致
                double scaledRadius = BallManipulationCore.ScaleRadius(
                    _config.MovementRadius,                    // 基础移动半径
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 计算位置修正值，用于精确定位
                int correctionValue = BallManipulationCore.ScaleCorrectionValue(
                    50,                                        // 基础修正值
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 第二步：计算移动方向
                var (dx, dy, slope) = BallManipulationCore.CalculateMouseDirection(mousePosition, windowRect);
                var (sx3, sy3) = BallManipulationCore.CalculateMovementParameters(scaledRadius, slope, dx);

                // 第三步：计算方向向量分量
                var (dwX, dwY) = BallManipulationCore.CalculateDirectionVector(directionVector.x, directionVector.y / directionVector.x);

                // 第四步：执行中分序列
                return ExecuteCenterSplitSequence(mousePosition, (sx3, sy3), (dwX, dwY), correctionValue, useVerticalSplit);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"中分操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行完整的中分序列
        /// 这是中分操作的核心执行逻辑
        /// </summary>
        /// <param name="centerPosition">操作中心位置</param>
        /// <param name="movementVector">移动向量 (sx3, sy3)</param>
        /// <param name="directionVector">方向向量 (dwX, dwY)</param>
        /// <param name="correctionValue">位置修正值</param>
        /// <param name="useVerticalSplit">是否使用垂直分割模式</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteCenterSplitSequence(Struct.POINT centerPosition, 
                                               (double sx3, double sy3) movementVector,
                                               (double dwX, double dwY) directionVector,
                                               int correctionValue,
                                               bool useVerticalSplit)
        {
            try
            {
                // 第一阶段：初始设置
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                Thread.Sleep(20);                                  // 短暂等待
                BallManipulationCore.SimulateMouseButton(true);    // 按下鼠标左键
                Thread.Sleep(20);                                  // 短暂等待

                // 第二阶段：重置到中心位置
                BallManipulationCore.SetCursorPosition(centerPosition.X, centerPosition.Y);  // 移动到中心
                Thread.Sleep(_config.FirstDragDelay);             // 等待第一次拖拽延迟

                // 第三阶段：第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FirstSplitDelay);

                // 第四阶段：准备移动
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                BallManipulationCore.SimulateMouseButton(true);   // 重新按下鼠标左键
                Thread.Sleep(_config.SecondDragDelay);            // 等待第二次拖拽延迟

                // 第五阶段：第二次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.SecondSplitDelay);

                // 第六阶段：计算最终位置并移动
                int finalX, finalY;
                if (useVerticalSplit)
                {
                    // 使用垂直分割计算
                    // 计算垂直于原方向的新斜率
                    double k = -1 / (directionVector.dwY / directionVector.dwX);
                    
                    // 计算新的移动参数
                    var (newSx3, newSy3) = BallManipulationCore.CalculateMovementParameters(
                        Math.Sqrt(movementVector.sx3 * movementVector.sx3 + movementVector.sy3 * movementVector.sy3), 
                        k, 
                        Math.Sign(movementVector.sx3));

                    // 计算垂直分割的最终位置
                    finalX = (int)(centerPosition.X + Math.Abs(newSx3) * Math.Sign(movementVector.sx3));
                    finalY = (int)(centerPosition.Y + Math.Abs(newSy3) * Math.Sign(movementVector.sy3));
                }
                else
                {
                    // 使用标准方向计算
                    finalX = (int)(centerPosition.X + movementVector.sx3 - directionVector.dwX * correctionValue);
                    finalY = (int)(centerPosition.Y + movementVector.sy3 - directionVector.dwY * correctionValue);
                }

                // 移动到最终位置
                BallManipulationCore.SetCursorPosition(finalX, finalY);
                Thread.Sleep(_config.ThirdDragDelay);             // 等待第三次拖拽延迟

                // 第七阶段：释放鼠标并完成序列
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键

                // 第八阶段：处理吐球功能（如果启用）
                if (_config.EnableBallEjection)
                {
                    ExecuteBallEjection();  // 执行吐球序列
                }

                // 第九阶段：执行最终分身序列
                ExecuteFinalSplitSequence();

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"中分序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行吐球序列
        /// 启动吐球功能的按键操作
        /// </summary>
        private void ExecuteBallEjection()
        {
            // 获取吐球按键的扫描码
            uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
            // 按下吐球按键（不立即释放，在最终序列中释放）
            API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, 0, 0);
        }

        /// <summary>
        /// 执行最终分身序列
        /// 进行多次分身操作以完成合球
        /// </summary>
        private void ExecuteFinalSplitSequence()
        {
            // 执行20次分身操作，确保球体完全合并
            for (int i = 0; i < 20; i++)
            {
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FinalSplitDelay);
            }

            // 如果启用了吐球功能，完成吐球操作
            if (_config.EnableBallEjection)
            {
                // 获取吐球按键的扫描码
                uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
                // 释放吐球按键
                API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
            }
        }

        /// <summary>
        /// 检查是否可以执行中分操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">当前按下的按键</param>
        /// <param name="windowHandle">当前前台窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改中分的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.CenterSplitConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                      // 更新启用状态
            _config.EnableBallEjection = newConfig.EnableBallEjection;    // 更新吐球设置
            _config.TriggerKey = newConfig.TriggerKey;                    // 更新触发按键
            _config.MovementRadius = newConfig.MovementRadius;            // 更新移动半径
            _config.FirstDragDelay = newConfig.FirstDragDelay;            // 更新第一次拖拽延迟
            _config.FirstSplitDelay = newConfig.FirstSplitDelay;          // 更新第一次分身延迟
            _config.SecondDragDelay = newConfig.SecondDragDelay;          // 更新第二次拖拽延迟
            _config.SecondSplitDelay = newConfig.SecondSplitDelay;        // 更新第二次分身延迟
            _config.ThirdDragDelay = newConfig.ThirdDragDelay;            // 更新第三次拖拽延迟
            _config.FinalSplitDelay = newConfig.FinalSplitDelay;          // 更新最终分身延迟
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的中分配置信息
        /// </summary>
        /// <returns>当前中分配置</returns>
        public BallMergingConfiguration.CenterSplitConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }

        /// <summary>
        /// 计算最优移动半径
        /// 根据当前条件计算最适合的操作半径
        /// </summary>
        /// <param name="windowRect">游戏窗口矩形</param>
        /// <param name="baseRadius">基础半径值</param>
        /// <returns>优化后的半径</returns>
        public double CalculateOptimalRadius(Struct.RECT windowRect, double baseRadius)
        {
            // 使用核心函数计算缩放后的半径
            return BallManipulationCore.ScaleRadius(
                baseRadius,                               // 基础半径
                windowRect.right - windowRect.left,      // 窗口宽度
                _globalConfig.JoystickSensitivity);       // 摇杆灵敏度
        }
    }
}
