/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 三角合球操作模块头文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 三角合球操作的头文件定义
 * • 基于C#版本TriangleBallMerger的完整移植
 * • 实现三角形阵型的球体合并功能
 * 
 * 🎯 功能特点:
 * • 🔺 三角形阵型球体合并 - 最经典的合球技术
 * • 📐 可配置阵型角度和时间延迟
 * • 🎯 支持箭头方向模式
 * • 💫 集成吐球功能
 * • 🔄 支持两种三角合球变体
 * • 📏 精确的位置计算和修正
 * 
 * 🔗 算法原理:
 * • 第一步：计算三角形顶点位置
 * • 第二步：按序移动到各个顶点
 * • 第三步：在每个顶点执行分身操作
 * • 第四步：执行最终合并序列
 * • 第五步：处理吐球功能（如果启用）
 * 
 * ⚠️ 使用注意:
 * • 需要明确的鼠标位置和窗口矩形
 * • 执行时避免鼠标移动干扰
 * • 注意与其他操作的时序配合
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#ifndef TRIANGLE_MERGE_H
#define TRIANGLE_MERGE_H

#include <stdbool.h>
#include <stdint.h>
#include "../core/ball_core.h"
#include "../config/config.h"

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 三角合球常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define TRIANGLE_MIN_ANGLE 30              // 最小三角形角度
#define TRIANGLE_MAX_ANGLE 120             // 最大三角形角度
#define TRIANGLE_MIN_RADIUS 20             // 最小三角形半径
#define TRIANGLE_MAX_RADIUS 150            // 最大三角形半径
#define TRIANGLE_FINAL_SPLIT_COUNT 20      // 最终分身次数
#define TRIANGLE_MAX_CORRECTION 50         // 最大修正偏移

// 三角合球执行状态
typedef enum {
    TRIANGLE_STATE_IDLE = 0,               // 空闲状态
    TRIANGLE_STATE_PREPARING,              // 准备阶段
    TRIANGLE_STATE_CALCULATING,            // 计算阶段
    TRIANGLE_STATE_MOVING,                 // 移动阶段
    TRIANGLE_STATE_SPLITTING,              // 分身阶段
    TRIANGLE_STATE_FINALIZING,             // 完成阶段
    TRIANGLE_STATE_ERROR                   // 错误状态
} TriangleExecutionState;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 三角合球数据结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 🔺 三角合球执行上下文
 * 包含执行过程中的所有状态信息
 */
typedef struct {
    // 配置信息
    TriangleMergingConfig config;          // 三角合球配置
    GlobalConfig global_config;            // 全局配置
    
    // 执行状态
    TriangleExecutionState state;          // 当前执行状态
    bool is_executing;                     // 是否正在执行
    uint64_t start_time;                   // 开始时间
    uint64_t last_operation_time;          // 最后操作时间
    
    // 位置信息
    Point mouse_position;                  // 鼠标位置
    Rectangle window_rect;                 // 窗口矩形
    Point center_position;                 // 中心位置
    TriangleVertices vertices;             // 三角形顶点
    
    // 计算参数
    DirectionVector direction_vector;      // 方向向量
    double formation_angle_rad;            // 阵型角度（弧度）
    int scaled_radius;                     // 缩放后的半径
    int current_vertex;                    // 当前顶点索引
    
    // 错误信息
    char last_error[256];                  // 最后错误信息
    int error_code;                        // 错误代码
} TriangleMergeContext;

/**
 * 🎯 三角合球结果结构
 * 包含执行结果和统计信息
 */
typedef struct {
    bool success;                          // 是否执行成功
    uint64_t execution_time;               // 执行时间（毫秒）
    int vertices_processed;                // 已处理顶点数
    int splits_executed;                   // 已执行分身数
    char result_message[256];              // 结果消息
    int final_error_code;                  // 最终错误代码
} TriangleMergeResult;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 三角合球核心函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 初始化三角合球上下文
 * @param context 上下文指针
 * @param triangle_config 三角合球配置
 * @param global_config 全局配置
 * @return 是否初始化成功
 */
bool triangle_merge_init_context(TriangleMergeContext* context,
                                const TriangleMergingConfig* triangle_config,
                                const GlobalConfig* global_config);

/**
 * 执行三角合球操作
 * @param context 上下文指针
 * @param mouse_pos 鼠标位置
 * @param window_rect 窗口矩形
 * @param result 执行结果输出
 * @return 是否执行成功
 */
bool triangle_merge_execute(TriangleMergeContext* context,
                           Point mouse_pos,
                           Rectangle window_rect,
                           TriangleMergeResult* result);

/**
 * 停止三角合球操作
 * @param context 上下文指针
 * @return 是否停止成功
 */
bool triangle_merge_stop(TriangleMergeContext* context);

/**
 * 重置三角合球上下文
 * @param context 上下文指针
 */
void triangle_merge_reset_context(TriangleMergeContext* context);

/**
 * 销毁三角合球上下文
 * @param context 上下文指针
 */
void triangle_merge_destroy_context(TriangleMergeContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 三角合球计算函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算三角形顶点位置
 * @param context 上下文指针
 * @return 是否计算成功
 */
bool triangle_merge_calculate_vertices(TriangleMergeContext* context);

/**
 * 计算方向向量和参数
 * @param context 上下文指针
 * @return 是否计算成功
 */
bool triangle_merge_calculate_direction(TriangleMergeContext* context);

/**
 * 应用位置修正
 * @param context 上下文指针
 * @param vertex_index 顶点索引
 * @param corrected_pos 修正后位置输出
 * @return 是否修正成功
 */
bool triangle_merge_apply_correction(TriangleMergeContext* context,
                                    int vertex_index,
                                    Point* corrected_pos);

/**
 * 验证三角形参数
 * @param context 上下文指针
 * @return 是否验证通过
 */
bool triangle_merge_validate_parameters(TriangleMergeContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 三角合球执行函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行准备阶段
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool triangle_merge_execute_preparation(TriangleMergeContext* context);

/**
 * 执行移动到顶点
 * @param context 上下文指针
 * @param vertex_index 顶点索引
 * @return 是否执行成功
 */
bool triangle_merge_execute_move_to_vertex(TriangleMergeContext* context, int vertex_index);

/**
 * 执行分身操作
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool triangle_merge_execute_split(TriangleMergeContext* context);

/**
 * 执行最终分身序列
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool triangle_merge_execute_final_split_sequence(TriangleMergeContext* context);

/**
 * 执行吐球操作
 * @param context 上下文指针
 * @return 是否执行成功
 */
bool triangle_merge_execute_ball_ejection(TriangleMergeContext* context);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 三角合球工具函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 获取当前执行状态
 * @param context 上下文指针
 * @return 当前执行状态
 */
TriangleExecutionState triangle_merge_get_state(const TriangleMergeContext* context);

/**
 * 检查是否正在执行
 * @param context 上下文指针
 * @return 是否正在执行
 */
bool triangle_merge_is_executing(const TriangleMergeContext* context);

/**
 * 获取执行进度百分比
 * @param context 上下文指针
 * @return 进度百分比（0-100）
 */
int triangle_merge_get_progress(const TriangleMergeContext* context);

/**
 * 获取状态信息字符串
 * @param context 上下文指针
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 是否获取成功
 */
bool triangle_merge_get_status_info(const TriangleMergeContext* context,
                                   char* buffer,
                                   size_t buffer_size);

/**
 * 设置错误信息
 * @param context 上下文指针
 * @param error_code 错误代码
 * @param error_message 错误消息
 */
void triangle_merge_set_error(TriangleMergeContext* context,
                             int error_code,
                             const char* error_message);

/**
 * 获取最后错误信息
 * @param context 上下文指针
 * @param error_code 错误代码输出
 * @param error_message 错误消息输出
 * @param message_size 消息缓冲区大小
 * @return 是否有错误
 */
bool triangle_merge_get_last_error(const TriangleMergeContext* context,
                                  int* error_code,
                                  char* error_message,
                                  size_t message_size);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎮 三角合球高级功能声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行箭头方向模式三角合球
 * @param context 上下文指针
 * @param arrow_direction 箭头方向向量
 * @param result 执行结果输出
 * @return 是否执行成功
 */
bool triangle_merge_execute_arrow_mode(TriangleMergeContext* context,
                                      DirectionVector arrow_direction,
                                      TriangleMergeResult* result);

/**
 * 执行自适应三角合球
 * @param context 上下文指针
 * @param adaptive_params 自适应参数
 * @param result 执行结果输出
 * @return 是否执行成功
 */
bool triangle_merge_execute_adaptive(TriangleMergeContext* context,
                                    void* adaptive_params,
                                    TriangleMergeResult* result);

/**
 * 预览三角形顶点位置
 * @param context 上下文指针
 * @param preview_vertices 预览顶点输出
 * @return 是否预览成功
 */
bool triangle_merge_preview_vertices(TriangleMergeContext* context,
                                    TriangleVertices* preview_vertices);

#endif // TRIANGLE_MERGE_H
