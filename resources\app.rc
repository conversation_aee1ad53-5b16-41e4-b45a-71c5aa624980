/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 资源文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • Windows资源文件定义
 * • 包含程序图标、版本信息、字符串资源等
 * • 用于编译生成程序的资源数据
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include <windows.h>

// 版本信息
#define VER_FILEVERSION             1,2,0,0
#define VER_FILEVERSION_STR         "*******\0"
#define VER_PRODUCTVERSION          1,2,0,0
#define VER_PRODUCTVERSION_STR      "1.2.0\0"
#define VER_COMPANYNAME_STR         "开源社区\0"
#define VER_FILEDESCRIPTION_STR     "球球大作战合球软件\0"
#define VER_INTERNALNAME_STR        "BallMergingSoftware\0"
#define VER_LEGALCOPYRIGHT_STR      "Copyright (C) 2024\0"
#define VER_ORIGINALFILENAME_STR    "ball_merging.exe\0"
#define VER_PRODUCTNAME_STR         "球球大作战合球软件\0"

// 程序图标（如果有图标文件）
// IDI_MAIN_ICON ICON "app_icon.ico"

// 版本信息资源
VS_VERSION_INFO VERSIONINFO
FILEVERSION     VER_FILEVERSION
PRODUCTVERSION  VER_PRODUCTVERSION
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEFLAGS       0x0L
FILEOS          VOS__WINDOWS32
FILETYPE        VFT_APP
FILESUBTYPE     VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "CompanyName",      VER_COMPANYNAME_STR
            VALUE "FileDescription", VER_FILEDESCRIPTION_STR
            VALUE "FileVersion",     VER_FILEVERSION_STR
            VALUE "InternalName",    VER_INTERNALNAME_STR
            VALUE "LegalCopyright",  VER_LEGALCOPYRIGHT_STR
            VALUE "OriginalFilename", VER_ORIGINALFILENAME_STR
            VALUE "ProductName",     VER_PRODUCTNAME_STR
            VALUE "ProductVersion",  VER_PRODUCTVERSION_STR
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END

// 字符串资源
STRINGTABLE
BEGIN
    1001    "球球大作战合球软件"
    1002    "版本 1.2.0"
    1003    "基于C语言开发的高性能合球软件"
    1004    "支持多种合球技术和自动化宏操作"
    1005    "运行"
    1006    "结束"
    1007    "吐球键"
    1008    "分身键"
    1009    "摇杆大小/秒"
    1010    "延时时间/毫秒"
    1011    "一键三角 可调角度"
    1012    "按键号"
    1013    "角度"
    1014    "拖动滑杆改变角度"
    1015    "合球时自动吐"
    1016    "直线"
    1017    "大炮"
    1018    "侧合为U"
    1019    "侧合左I"
    1020    "二键一式"
    1021    "侧合"
    1022    "四分"
    1023    "旋转"
    1024    "后仰"
    1025    "三键一式"
    1026    "中分寸"
    1027    "四分寸"
    1028    "仙人指"
    1029    "状态：正在运行"
    1030    "状态：已停止"
    1031    "配置已保存"
    1032    "配置加载失败"
    1033    "参数验证失败"
    1034    "操作执行成功"
    1035    "操作执行失败"
END

// 对话框资源（如果需要）
/*
IDD_ABOUT DIALOGEX 0, 0, 300, 200
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "关于球球大作战合球软件"
FONT 9, "宋体"
BEGIN
    DEFPUSHBUTTON   "确定", IDOK, 125, 170, 50, 14
    LTEXT           "球球大作战合球软件", IDC_STATIC, 20, 20, 260, 20
    LTEXT           "版本 1.2.0", IDC_STATIC, 20, 40, 260, 10
    LTEXT           "基于C语言开发的高性能合球软件", IDC_STATIC, 20, 60, 260, 10
    LTEXT           "支持多种合球技术和自动化宏操作", IDC_STATIC, 20, 80, 260, 10
    LTEXT           "Copyright (C) 2024 开源社区", IDC_STATIC, 20, 120, 260, 10
    LTEXT           "技术支持: 开源社区", IDC_STATIC, 20, 140, 260, 10
END
*/

// 菜单资源（如果需要）
/*
IDR_MAIN_MENU MENU
BEGIN
    POPUP "文件(&F)"
    BEGIN
        MENUITEM "加载配置(&L)...", ID_FILE_LOAD_CONFIG
        MENUITEM "保存配置(&S)...", ID_FILE_SAVE_CONFIG
        MENUITEM SEPARATOR
        MENUITEM "退出(&X)", ID_FILE_EXIT
    END
    POPUP "操作(&O)"
    BEGIN
        MENUITEM "开始运行(&S)", ID_OPERATION_START
        MENUITEM "停止运行(&T)", ID_OPERATION_STOP
        MENUITEM SEPARATOR
        MENUITEM "重置配置(&R)", ID_OPERATION_RESET
    END
    POPUP "帮助(&H)"
    BEGIN
        MENUITEM "使用说明(&H)", ID_HELP_USAGE
        MENUITEM "关于(&A)...", ID_HELP_ABOUT
    END
END
*/

// 加速键表（如果需要）
/*
IDR_ACCELERATOR ACCELERATORS
BEGIN
    "S", ID_OPERATION_START, VIRTKEY, CONTROL
    "T", ID_OPERATION_STOP, VIRTKEY, CONTROL
    "L", ID_FILE_LOAD_CONFIG, VIRTKEY, CONTROL
    "S", ID_FILE_SAVE_CONFIG, VIRTKEY, CONTROL, SHIFT
    VK_F1, ID_HELP_USAGE, VIRTKEY
END
*/
