/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎛️ 杠杆宏处理器 (LeverMacroProcessor.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现杠杆宏功能的双击检测和执行，这是一个智能的宏操作系统：                            │
 * │                                                                                     │
 * │ 🎯 核心功能:                                                                        │
 * │   • 双击检测 - 精确的时间窗口双击检测机制                                            │
 * │   • 智能执行 - 根据按键时机执行不同的操作序列                                        │
 * │   • 状态管理 - 完善的按键状态跟踪和管理                                              │
 * │   • 时间控制 - 可配置的时间窗口和延迟参数                                            │
 * │                                                                                     │
 * │ 🔄 操作逻辑:                                                                        │
 * │   第一次按键:                                                                       │
 * │   • 执行2次分身操作                                                                 │
 * │   • 启动计时器，开始双击检测窗口                                                     │
 * │   • 记录按键时间，设置状态标志                                                       │
 * │                                                                                     │
 * │   第二次按键（时间窗口内）:                                                         │
 * │   • 执行4次分身操作（完整杠杆宏）                                                    │
 * │   • 停止计时器，重置状态                                                             │
 * │   • 输出调试信息确认双击成功                                                         │
 * │                                                                                     │
 * │ ⏱️ 时间机制:                                                                        │
 * │   • 默认检测窗口: 1000毫秒（1秒）                                                    │
 * │   • 可配置的分身间延迟                                                               │
 * │   • 自动超时重置机制                                                                 │
 * │   • 高精度时间戳记录                                                                 │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 精确计时 - 使用高精度计时器确保准确的双击检测
 * • 状态安全 - 线程安全的状态管理机制
 * • 可配置性 - 支持自定义时间窗口和延迟参数
 * • 调试友好 - 详细的状态信息和调试输出
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 按键模拟功能
 * • 使用 LeverMacroConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局按键设置
 * • 使用 System.Timers.Timer - 时间窗口控制
 *
 * 💡 使用技巧:
 * • 时间窗口可根据个人习惯调整（800-1500ms）
 * • 分身延迟可根据网络状况优化（30-80ms）
 * • 可以通过GetStateInfo()监控运行状态
 * • 支持不同场景的参数预设
 *
 * ⚠️ 注意事项:
 * • 双击速度不能过快，需要在时间窗口内
 * • 避免与其他宏操作的按键冲突
 * • 计时器资源需要正确释放
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using System.Timers;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 杠杆宏处理器 - 处理杠杆宏功能的双击检测和执行
    /// 这个类实现了杠杆宏的完整操作流程，包括双击检测机制
    /// </summary>
    public class LeverMacroProcessor
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.LeverMacroConfig _config;         // 杠杆宏配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;      // 全局配置
        
        // 双击检测的状态管理
        private bool _isFirstKeyPress = false;                  // 是否是第一次按键
        private DateTime _firstKeyPressTime = DateTime.MinValue; // 第一次按键的时间
        private System.Timers.Timer _keyPressTimer;            // 按键计时器

        /// <summary>
        /// 构造函数 - 初始化杠杆宏处理器
        /// </summary>
        /// <param name="config">杠杆宏配置</param>
        /// <param name="globalConfig">全局配置</param>
        public LeverMacroProcessor(BallMergingConfiguration.LeverMacroConfig config, 
                                  BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
            
            // 初始化计时器
            InitializeTimer();
        }

        /// <summary>
        /// 初始化按键计时器
        /// 用于双击检测的时间间隔控制
        /// </summary>
        private void InitializeTimer()
        {
            // 创建计时器，设置间隔时间
            _keyPressTimer = new System.Timers.Timer(_config.TimeIntervalForDoublePress);
            
            // 绑定计时器到期事件
            _keyPressTimer.Elapsed += OnTimerElapsed;
            
            // 设置计时器不自动重复
            _keyPressTimer.AutoReset = false;
        }

        /// <summary>
        /// 计时器到期事件处理
        /// 当双击检测时间窗口结束时重置状态
        /// </summary>
        /// <param name="sender">计时器对象</param>
        /// <param name="e">到期事件参数</param>
        private void OnTimerElapsed(object sender, ElapsedEventArgs e)
        {
            // 重置第一次按键状态，表示双击检测窗口已关闭
            _isFirstKeyPress = false;
        }

        /// <summary>
        /// 执行杠杆宏操作的主函数
        /// 这是杠杆宏功能的入口点，包含双击检测逻辑
        /// </summary>
        /// <returns>操作是否成功</returns>
        public bool ExecuteLeverMacro()
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 获取当前时间
                DateTime currentTime = DateTime.Now;
                
                // 计算距离第一次按键的时间间隔
                double timeSinceFirstPress = (currentTime - _firstKeyPressTime).TotalMilliseconds;

                // 判断是否在双击检测时间窗口内
                if (_isFirstKeyPress && timeSinceFirstPress <= _config.TimeIntervalForDoublePress)
                {
                    // 第二次按键且在时间窗口内 - 执行完整杠杆宏
                    return ExecuteFullLeverMacro();
                }
                else
                {
                    // 第一次按键或超出时间窗口 - 执行部分杠杆宏
                    return ExecutePartialLeverMacro(currentTime);
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"杠杆宏操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行完整杠杆宏序列
        /// 当检测到双击时执行的完整操作
        /// </summary>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteFullLeverMacro()
        {
            try
            {
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine("执行完整杠杆宏（双击检测成功）");

                // 执行四次分身操作，每次之间有延迟
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.DelayBetweenSplits);  // 第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.DelayBetweenSplits);  // 第二次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.DelayBetweenSplits);  // 第三次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, 0);                           // 第四次分身（无延迟）

                // 重置双击检测状态
                _isFirstKeyPress = false;  // 清除第一次按键标志
                _keyPressTimer.Stop();     // 停止计时器

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"完整杠杆宏序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行部分杠杆宏序列
        /// 当第一次按键或超出双击时间窗口时执行的操作
        /// </summary>
        /// <param name="currentTime">当前时间戳</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecutePartialLeverMacro(DateTime currentTime)
        {
            try
            {
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine("执行部分杠杆宏（第一次按键）");

                // 执行两次分身操作
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.DelayBetweenSplits);  // 第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, 0);                           // 第二次分身（无延迟）

                // 设置双击检测状态
                _firstKeyPressTime = currentTime;  // 记录第一次按键时间
                _isFirstKeyPress = true;           // 设置第一次按键标志
                _keyPressTimer.Start();            // 启动计时器

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"部分杠杆宏序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 检查是否可以执行杠杆宏操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">当前按下的按键</param>
        /// <param name="windowHandle">当前前台窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改杠杆宏的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.LeverMacroConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 如果时间间隔发生变化，更新计时器
            if (_config.TimeIntervalForDoublePress != newConfig.TimeIntervalForDoublePress)
            {
                _keyPressTimer.Interval = newConfig.TimeIntervalForDoublePress;  // 更新计时器间隔
            }

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                                      // 更新启用状态
            _config.TriggerKey = newConfig.TriggerKey;                                    // 更新触发按键
            _config.DelayBetweenSplits = newConfig.DelayBetweenSplits;                    // 更新分身间延迟
            _config.TimeIntervalForDoublePress = newConfig.TimeIntervalForDoublePress;    // 更新双击检测间隔
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的杠杆宏配置信息
        /// </summary>
        /// <returns>当前杠杆宏配置</returns>
        public BallMergingConfiguration.LeverMacroConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }

        /// <summary>
        /// 重置杠杆宏状态
        /// 清除所有双击检测状态
        /// </summary>
        public void ResetState()
        {
            _isFirstKeyPress = false;                       // 清除第一次按键标志
            _firstKeyPressTime = DateTime.MinValue;         // 重置第一次按键时间
            _keyPressTimer.Stop();                          // 停止计时器
        }

        /// <summary>
        /// 获取当前状态信息
        /// 用于调试和状态监控
        /// </summary>
        /// <returns>格式化的状态信息字符串</returns>
        public string GetStateInfo()
        {
            return $"第一次按键状态: {_isFirstKeyPress}, " +
                   $"第一次按键时间: {_firstKeyPressTime}, " +
                   $"计时器运行状态: {_keyPressTimer.Enabled}, " +
                   $"时间间隔: {_config.TimeIntervalForDoublePress}毫秒";
        }

        /// <summary>
        /// 验证杠杆宏参数
        /// 检查配置参数是否合理
        /// </summary>
        /// <returns>参数是否有效</returns>
        public bool ValidateParameters()
        {
            // 检查分身间延迟是否合理
            if (_config.DelayBetweenSplits < 0 || _config.DelayBetweenSplits > 1000)
            {
                return false;  // 延迟值不合理
            }

            // 检查双击检测时间间隔是否合理
            if (_config.TimeIntervalForDoublePress < 100 || _config.TimeIntervalForDoublePress > 5000)
            {
                return false;  // 时间间隔不合理
            }

            return true;  // 所有参数都有效
        }

        /// <summary>
        /// 获取不同使用场景的推荐设置
        /// 根据不同的使用场景提供优化的配置
        /// </summary>
        /// <param name="scenario">使用场景</param>
        /// <returns>推荐的配置</returns>
        public BallMergingConfiguration.LeverMacroConfig GetRecommendedSettings(string scenario)
        {
            // 创建新的配置对象
            var config = new BallMergingConfiguration.LeverMacroConfig();

            // 根据场景类型设置不同的参数
            switch (scenario.ToLower())
            {
                case "fast":  // 快速模式
                    config.DelayBetweenSplits = 30;           // 较短的分身间延迟
                    config.TimeIntervalForDoublePress = 800;  // 较短的双击检测间隔
                    break;

                case "precise":  // 精确模式
                    config.DelayBetweenSplits = 80;           // 较长的分身间延迟
                    config.TimeIntervalForDoublePress = 1500; // 较长的双击检测间隔
                    break;

                case "balanced":  // 平衡模式
                default:
                    config.DelayBetweenSplits = 50;           // 中等分身间延迟
                    config.TimeIntervalForDoublePress = 1000; // 中等双击检测间隔
                    break;
            }

            return config;  // 返回推荐配置
        }

        /// <summary>
        /// 根据系统性能计算最优延迟
        /// 动态调整延迟以适应不同的系统性能
        /// </summary>
        /// <param name="baseDelay">基础延迟值</param>
        /// <param name="systemPerformanceFactor">系统性能因子（0.5到2.0）</param>
        /// <returns>优化后的延迟值</returns>
        public int CalculateOptimalDelay(int baseDelay, double systemPerformanceFactor)
        {
            // 限制性能因子在合理范围内
            systemPerformanceFactor = Math.Max(0.5, Math.Min(2.0, systemPerformanceFactor));
            
            // 根据系统性能调整延迟
            // 性能越低延迟越高，性能越高延迟越低
            double adjustedDelay = baseDelay / systemPerformanceFactor;
            
            // 返回调整后的延迟值
            return (int)Math.Round(adjustedDelay);
        }

        /// <summary>
        /// 释放资源
        /// 清理计时器等资源
        /// </summary>
        public void Dispose()
        {
            _keyPressTimer?.Stop();     // 停止计时器
            _keyPressTimer?.Dispose();  // 释放计时器资源
        }
    }
}
