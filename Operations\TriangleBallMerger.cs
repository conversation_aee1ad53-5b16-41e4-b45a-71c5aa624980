/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🔺 三角合球操作类 (TriangleBallMerger.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现三角形阵型的球体合并功能，这是最经典的合球技术之一：                              │
 * │                                                                                     │
 * │ 🎯 核心功能:                                                                        │
 * │   • 三角阵型计算 - 根据角度和半径计算三个顶点位置                                    │
 * │   • 序列化执行 - 按照特定顺序移动到各个顶点并分身                                    │
 * │   • 方向适配 - 支持箭头方向模式和鼠标方向模式                                        │
 * │   • 吐球集成 - 可选的吐球功能集成                                                   │
 * │                                                                                     │
 * │ 🔄 操作流程:                                                                        │
 * │   1. 准备阶段 - 释放并重新按下鼠标，等待初始延迟                                     │
 * │   2. 第一顶点 - 移动到三角形第一个顶点并执行分身                                     │
 * │   3. 第二顶点 - 移动到三角形第二个顶点并执行分身                                     │
 * │   4. 第三顶点 - 移动到三角形第三个顶点                                               │
 * │   5. 完成阶段 - 释放鼠标，执行吐球（可选），进行最终分身序列                         │
 * │                                                                                     │
 * │ ⚙️ 可配置参数:                                                                      │
 * │   • 阵型角度 - 三角形的张开角度（默认60度）                                          │
 * │   • 移动半径 - 三角形的大小（默认100像素）                                           │
 * │   • 时间延迟 - 各个步骤之间的等待时间                                                │
 * │   • 箭头方向 - 是否使用鼠标拖拽方向                                                  │
 * │   • 吐球功能 - 是否启用吐球操作                                                     │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 精确计算 - 使用三角函数精确计算顶点位置
 * • 分辨率适配 - 自动根据窗口大小调整操作参数
 * • 错误处理 - 完善的异常捕获和错误恢复
 * • 状态管理 - 清晰的操作状态跟踪
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 数学计算和输入控制
 * • 使用 TriangleMergingConfig - 配置参数管理
 * • 使用 GlobalConfig - 全局设置（按键、灵敏度等）
 *
 * 💡 使用技巧:
 * • 角度设置建议在30-90度之间，60度效果最佳
 * • 半径可根据游戏窗口大小调整，建议80-150像素
 * • 时间延迟可根据网络延迟和系统性能调整
 *
 * ⚠️ 注意事项:
 * • 执行前确保鼠标在游戏窗口内
 * • 避免在球体移动过程中执行
 * • 注意与其他操作的按键冲突
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Operations
{
    /// <summary>
    /// 三角合球操作类 - 处理三角形阵型的球体合并功能
    /// 这个类实现了三角合球的完整操作流程
    /// </summary>
    public class TriangleBallMerger
    {
        // 私有字段，存储配置信息
        private readonly BallMergingConfiguration.TriangleMergingConfig _config;      // 三角合球配置
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;        // 全局配置

        /// <summary>
        /// 构造函数 - 初始化三角合球操作器
        /// </summary>
        /// <param name="config">三角合球配置</param>
        /// <param name="globalConfig">全局配置</param>
        public TriangleBallMerger(BallMergingConfiguration.TriangleMergingConfig config, 
                                 BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空，如果为空则抛出异常
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
        }

        /// <summary>
        /// 执行三角合球操作的主函数
        /// 这是三角合球功能的入口点
        /// </summary>
        /// <param name="mousePosition">当前鼠标位置</param>
        /// <param name="windowRect">游戏窗口矩形区域</param>
        /// <param name="directionVector">可选的方向向量（如果使用箭头方向）</param>
        /// <returns>操作是否成功</returns>
        public bool ExecuteTriangleMerging(Struct.POINT mousePosition, Struct.RECT windowRect, 
                                         (double x, double y)? directionVector = null)
        {
            // 检查功能是否启用
            if (!_config.IsEnabled)
                return false;  // 功能未启用，直接返回失败

            try
            {
                // 第一步：计算缩放后的半径
                // 根据窗口大小调整操作半径，确保在不同分辨率下效果一致
                double scaledRadius = BallManipulationCore.ScaleRadius(
                    _config.MovementRadius,                    // 基础移动半径
                    windowRect.right - windowRect.left,       // 窗口宽度
                    _globalConfig.JoystickSensitivity);        // 摇杆灵敏度

                // 第二步：确定移动方向
                double dx, dy, slope;  // 方向分量和斜率
                if (_config.UseArrowDirection && directionVector.HasValue)
                {
                    // 使用箭头方向（鼠标拖拽方向）
                    dx = directionVector.Value.x;
                    dy = directionVector.Value.y;
                    slope = dy / dx;  // 计算斜率
                }
                else
                {
                    // 使用鼠标相对于窗口中心的方向
                    (dx, dy, slope) = BallManipulationCore.CalculateMouseDirection(mousePosition, windowRect);
                }

                // 第三步：计算移动参数
                // 根据半径和方向计算具体的移动距离
                var (sx3, sy3) = BallManipulationCore.CalculateMovementParameters(scaledRadius, slope, dx);

                // 第四步：计算三角形阵型的三个点位置
                var trianglePoints = CalculateTrianglePoints(sx3, sy3, _config.FormationAngle);

                // 第五步：执行三角合球序列
                return ExecuteTriangleMergingSequence(mousePosition, trianglePoints);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"三角合球操作失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 计算三角形阵型的三个点位置
        /// 根据基础移动向量和角度计算三角形的三个顶点
        /// </summary>
        /// <param name="sx3">基础X方向移动分量</param>
        /// <param name="sy3">基础Y方向移动分量</param>
        /// <param name="formationAngle">三角阵型角度</param>
        /// <returns>三角形三个顶点的坐标</returns>
        private ((double x1, double y1), (double x2, double y2), (double x3, double y3)) CalculateTrianglePoints(
            double sx3, double sy3, int formationAngle)
        {
            // 将角度转换为弧度（除以360是因为要计算半角）
            double angleRad = formationAngle * BallManipulationCore.PI / 360;
            
            // 计算第一个三角形顶点（逆时针旋转）
            double sx1 = (sx3 * Math.Cos(angleRad) - sy3 * Math.Sin(angleRad));
            double sy1 = (sy3 * Math.Cos(angleRad) + sx3 * Math.Sin(angleRad));
            
            // 计算第二个三角形顶点（顺时针旋转）
            double sx2 = (sx3 * Math.Cos(angleRad) + sy3 * Math.Sin(angleRad));
            double sy2 = (sy3 * Math.Cos(angleRad) - sx3 * Math.Sin(angleRad));

            // 返回三个顶点的相对坐标
            return ((sx1, sy1), (sx2, sy2), (sx3, sy3));
        }

        /// <summary>
        /// 执行完整的三角合球序列
        /// 这是三角合球的核心执行逻辑
        /// </summary>
        /// <param name="centerPosition">三角形中心位置</param>
        /// <param name="trianglePoints">三角形三个顶点</param>
        /// <returns>序列是否执行成功</returns>
        private bool ExecuteTriangleMergingSequence(Struct.POINT centerPosition, 
            ((double x1, double y1), (double x2, double y2), (double x3, double y3)) trianglePoints)
        {
            try
            {
                // 第一阶段：准备阶段
                // 释放鼠标按钮并准备开始序列
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键
                Thread.Sleep(20);                                  // 短暂等待
                BallManipulationCore.SimulateMouseButton(true);    // 按下鼠标左键
                Thread.Sleep(_config.InitialDelay);               // 等待初始延迟

                // 第二阶段：移动到第一个三角形顶点并分身
                BallManipulationCore.SetCursorPosition(
                    centerPosition.X + (int)trianglePoints.x1,    // 计算第一个点的X坐标
                    centerPosition.Y + (int)trianglePoints.y1);   // 计算第一个点的Y坐标
                Thread.Sleep(_config.FirstMoveDelay);             // 等待移动延迟
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FirstSplitDelay);  // 执行分身

                // 第三阶段：移动到第二个三角形顶点并分身
                BallManipulationCore.SetCursorPosition(
                    centerPosition.X + (int)trianglePoints.x2,    // 计算第二个点的X坐标
                    centerPosition.Y + (int)trianglePoints.y2);   // 计算第二个点的Y坐标
                Thread.Sleep(_config.SecondMoveDelay);            // 等待移动延迟
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.SecondSplitDelay); // 执行分身

                // 第四阶段：移动到第三个三角形顶点
                BallManipulationCore.SetCursorPosition(
                    centerPosition.X + (int)trianglePoints.x3,    // 计算第三个点的X坐标
                    centerPosition.Y + (int)trianglePoints.y3);   // 计算第三个点的Y坐标
                Thread.Sleep(_config.FinalMoveDelay);             // 等待移动延迟

                // 第五阶段：释放鼠标按钮
                BallManipulationCore.SimulateMouseButton(false);  // 释放鼠标左键

                // 第六阶段：处理吐球功能（如果启用）
                if (_config.EnableBallEjection)
                {
                    ExecuteBallEjection();  // 执行吐球序列
                }

                // 第七阶段：执行最终分身序列
                ExecuteFinalSplitSequence();

                return true;  // 操作成功完成
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"三角合球序列执行失败: {ex.Message}");
                return false;  // 返回失败
            }
        }

        /// <summary>
        /// 执行吐球序列
        /// 启动吐球功能的按键操作
        /// </summary>
        private void ExecuteBallEjection()
        {
            // 获取吐球按键的扫描码
            uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
            // 按下吐球按键（不立即释放，在最终序列中释放）
            API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, 0, 0);
        }

        /// <summary>
        /// 执行最终分身序列
        /// 进行多次分身操作以完成合球
        /// </summary>
        private void ExecuteFinalSplitSequence()
        {
            // 执行20次分身操作，确保球体完全合并
            for (int i = 0; i < 20; i++)
            {
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, _config.FinalSplitDelay);
            }

            // 如果启用了吐球功能，完成吐球操作
            if (_config.EnableBallEjection)
            {
                // 获取吐球按键的扫描码
                uint scancode = API.MapVirtualKeyW(_globalConfig.BallEjectionKey, 0);
                // 释放吐球按键
                API.keybd_event(_globalConfig.BallEjectionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
            }
        }

        /// <summary>
        /// 检查是否可以执行三角合球操作
        /// 验证当前条件是否满足执行要求
        /// </summary>
        /// <param name="triggerKey">当前按下的按键</param>
        /// <param name="windowHandle">当前前台窗口句柄</param>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <returns>是否可以执行</returns>
        public bool CanExecute(int triggerKey, IntPtr windowHandle, IntPtr gameWindowHandle)
        {
            // 检查三个条件：功能启用、按键匹配、窗口匹配
            return _config.IsEnabled &&                    // 功能必须启用
                   triggerKey == _config.TriggerKey &&     // 按键必须匹配
                   windowHandle == gameWindowHandle;       // 必须在游戏窗口中
        }

        /// <summary>
        /// 更新配置设置
        /// 允许在运行时修改三角合球的参数
        /// </summary>
        /// <param name="newConfig">新的配置参数</param>
        public void UpdateConfiguration(BallMergingConfiguration.TriangleMergingConfig newConfig)
        {
            // 检查新配置是否为空
            if (newConfig == null)
                throw new ArgumentNullException(nameof(newConfig));

            // 复制所有配置值到当前配置
            _config.IsEnabled = newConfig.IsEnabled;                      // 更新启用状态
            _config.UseArrowDirection = newConfig.UseArrowDirection;      // 更新箭头方向设置
            _config.EnableBallEjection = newConfig.EnableBallEjection;    // 更新吐球设置
            _config.TriggerKey = newConfig.TriggerKey;                    // 更新触发按键
            _config.FormationAngle = newConfig.FormationAngle;            // 更新阵型角度
            _config.MovementRadius = newConfig.MovementRadius;            // 更新移动半径
            _config.InitialDelay = newConfig.InitialDelay;                // 更新初始延迟
            _config.FirstMoveDelay = newConfig.FirstMoveDelay;            // 更新第一次移动延迟
            _config.FirstSplitDelay = newConfig.FirstSplitDelay;          // 更新第一次分身延迟
            _config.SecondMoveDelay = newConfig.SecondMoveDelay;          // 更新第二次移动延迟
            _config.SecondSplitDelay = newConfig.SecondSplitDelay;        // 更新第二次分身延迟
            _config.FinalMoveDelay = newConfig.FinalMoveDelay;            // 更新最终移动延迟
            _config.FinalSplitDelay = newConfig.FinalSplitDelay;          // 更新最终分身延迟
        }

        /// <summary>
        /// 获取当前配置
        /// 返回当前的三角合球配置信息
        /// </summary>
        /// <returns>当前三角合球配置</returns>
        public BallMergingConfiguration.TriangleMergingConfig GetConfiguration()
        {
            return _config;  // 返回配置对象
        }
    }
}
