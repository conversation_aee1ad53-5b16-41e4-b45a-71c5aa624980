/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🤖 球体宏操作类 (BallMacroOperations.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 实现各种自动化球体宏操作，提供强大的自动化分割和连续动作功能：                        │
 * │                                                                                     │
 * │ 🎯 分割宏系列:                                                                      │
 * │   • 二分宏 - 1次分身，创建2个球体                                                    │
 * │   • 四分宏 - 2次分身，创建4个球体                                                    │
 * │   • 八分宏 - 3次分身，创建8个球体                                                    │
 * │   • 十六分宏 - 4次分身，创建16个球体                                                 │
 * │   • 原地宏 - 特殊的原地分身，包含摇杆重置                                            │
 * │                                                                                     │
 * │ 🔄 连续动作宏:                                                                      │
 * │   • 长按宏1/2 - 持续按住指定按键                                                     │
 * │   • 连击宏1/2 - 快速重复按键操作                                                     │
 * │   • 多线程执行 - 每个连续动作在独立线程中运行                                        │
 * │   • 状态管理 - 实时跟踪每个宏的激活状态                                              │
 * │                                                                                     │
 * │ ⚙️ 配置系统:                                                                        │
 * │   • SplitMacroConfig - 分割宏配置（次数、延迟等）                                    │
 * │   • ContinuousActionConfig - 连续动作配置（间隔、模式等）                           │
 * │   • 独立配置 - 每个宏都有独立的配置对象                                              │
 * │   • 实时调整 - 支持运行时修改配置参数                                                │
 * │                                                                                     │
 * │ 🧵 线程管理:                                                                        │
 * │   • 主线程处理 - 分割宏在主线程中同步执行                                            │
 * │   • 后台线程 - 连续动作在后台线程中异步执行                                          │
 * │   • 状态同步 - 线程安全的状态管理机制                                                │
 * │   • 资源清理 - 自动清理线程资源                                                     │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 高度自动化 - 一键执行复杂的操作序列
 * • 线程安全 - 多线程环境下的安全操作
 * • 灵活配置 - 支持细粒度的参数调整
 * • 状态监控 - 实时状态跟踪和调试支持
 *
 * 🔗 依赖关系:
 * • 使用 BallManipulationCore - 按键模拟和基础操作
 * • 使用 GlobalConfig - 全局按键设置
 * • 使用 System.Threading - 多线程支持
 * • 使用 Vanara.PInvoke - Windows API调用
 *
 * 💡 使用技巧:
 * • 分割宏适合快速创建多个球体
 * • 连续动作适合需要持续操作的场景
 * • 可以组合使用多种宏实现复杂操作
 * • 通过状态监控可以调试宏的执行情况
 *
 * ⚠️ 注意事项:
 * • 避免同时启用冲突的宏操作
 * • 连续动作会持续占用系统资源
 * • 确保按键不与游戏操作冲突
 * • 及时重置状态避免资源泄漏
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;
using System.Threading;
using Vanara.PInvoke;
using ANYE_Balls.BallMerging.Core;
using ANYE_Balls.BallMerging.Configuration;

namespace ANYE_Balls.BallMerging.Macros
{
    /// <summary>
    /// 球体宏操作类 - 处理各种球体宏操作，包括分割宏和连续动作
    /// 这个类实现了自动化球体分割序列的功能
    /// </summary>
    public class BallMacroOperations
    {
        #region 宏配置类定义
        
        /// <summary>
        /// 分割宏配置类
        /// 用于配置分割宏操作的参数
        /// </summary>
        public class SplitMacroConfig
        {
            public bool IsEnabled { get; set; }          // 是否启用此宏
            public int TriggerKey { get; set; }          // 触发按键
            public int DelayBetweenSplits { get; set; }  // 分身之间的延迟
            public int NumberOfSplits { get; set; }      // 分身次数
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            /// <param name="numberOfSplits">分身次数</param>
            /// <param name="delay">延迟时间</param>
            public SplitMacroConfig(int numberOfSplits = 1, int delay = 50)
            {
                NumberOfSplits = numberOfSplits;    // 设置分身次数
                DelayBetweenSplits = delay;          // 设置延迟时间
                IsEnabled = false;                   // 默认不启用
            }
        }

        /// <summary>
        /// 连续动作配置类
        /// 用于配置连续动作宏的参数
        /// </summary>
        public class ContinuousActionConfig
        {
            public bool IsEnabled { get; set; }         // 是否启用此宏
            public int TriggerKey { get; set; }         // 触发按键
            public byte ActionKey { get; set; }         // 动作按键
            public int ActionInterval { get; set; }     // 动作间隔
            public bool IsHoldAction { get; set; }      // 是否为长按动作（true为长按，false为重复按键）
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public ContinuousActionConfig()
            {
                IsEnabled = false;       // 默认不启用
                ActionInterval = 50;     // 默认50毫秒间隔
                IsHoldAction = false;    // 默认为重复按键模式
            }
        }
        #endregion

        #region 私有字段
        private readonly BallMergingConfiguration.GlobalConfig _globalConfig;  // 全局配置
        
        // 分割宏配置实例
        public SplitMacroConfig TwoSplitMacro { get; set; }      // 二分宏
        public SplitMacroConfig FourSplitMacro { get; set; }     // 四分宏
        public SplitMacroConfig EightSplitMacro { get; set; }    // 八分宏
        public SplitMacroConfig SixteenSplitMacro { get; set; }  // 十六分宏
        public SplitMacroConfig OriginMacro { get; set; }        // 原地宏
        
        // 连续动作配置实例
        public ContinuousActionConfig ContinuousAction1 { get; set; } // 长按宏1
        public ContinuousActionConfig ContinuousAction2 { get; set; } // 长按宏2
        public ContinuousActionConfig ComboAction1 { get; set; }      // 连击宏1
        public ContinuousActionConfig ComboAction2 { get; set; }      // 连击宏2

        // 连续动作的状态跟踪
        private bool _continuousAction1Active = false;  // 长按宏1是否激活
        private bool _continuousAction2Active = false;  // 长按宏2是否激活
        private bool _comboAction1Active = false;       // 连击宏1是否激活
        private bool _comboAction2Active = false;       // 连击宏2是否激活
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数 - 初始化球体宏操作器
        /// </summary>
        /// <param name="globalConfig">全局配置</param>
        public BallMacroOperations(BallMergingConfiguration.GlobalConfig globalConfig)
        {
            // 检查配置参数是否为空
            _globalConfig = globalConfig ?? throw new ArgumentNullException(nameof(globalConfig));
            
            // 初始化所有配置
            InitializeConfigurations();
        }

        /// <summary>
        /// 初始化所有宏配置
        /// 设置各种宏的默认参数
        /// </summary>
        private void InitializeConfigurations()
        {
            // 初始化分割宏配置
            TwoSplitMacro = new SplitMacroConfig(1, 50);      // 二分宏：1次分身，50毫秒延迟
            FourSplitMacro = new SplitMacroConfig(2, 50);     // 四分宏：2次分身，50毫秒延迟
            EightSplitMacro = new SplitMacroConfig(3, 50);    // 八分宏：3次分身，50毫秒延迟
            SixteenSplitMacro = new SplitMacroConfig(4, 50);  // 十六分宏：4次分身，50毫秒延迟
            OriginMacro = new SplitMacroConfig(2, 50);        // 原地宏：2次分身，50毫秒延迟
            
            // 初始化连续动作配置
            ContinuousAction1 = new ContinuousActionConfig { IsHoldAction = true };   // 长按宏1
            ContinuousAction2 = new ContinuousActionConfig { IsHoldAction = true };   // 长按宏2
            ComboAction1 = new ContinuousActionConfig { IsHoldAction = false };       // 连击宏1
            ComboAction2 = new ContinuousActionConfig { IsHoldAction = false };       // 连击宏2
        }
        #endregion

        #region 主处理方法
        /// <summary>
        /// 处理所有宏操作
        /// 这是宏操作的主入口点
        /// </summary>
        /// <param name="gameWindowHandle">游戏窗口句柄</param>
        /// <param name="isGameActive">游戏是否激活</param>
        public void ProcessMacroOperations(IntPtr gameWindowHandle, bool isGameActive)
        {
            // 检查游戏是否激活
            if (!isGameActive || gameWindowHandle == IntPtr.Zero)
                return;  // 游戏未激活，直接返回

            try
            {
                // 检查当前前台窗口是否为游戏窗口
                IntPtr foregroundWindow = API.GetForegroundWindow();
                if (foregroundWindow != gameWindowHandle)
                    return;  // 不在游戏窗口中，直接返回

                // 处理分割宏
                ProcessSplitMacros();
                
                // 处理连续动作
                ProcessContinuousActions();
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"宏操作处理失败: {ex.Message}");
            }
        }
        #endregion

        #region 分割宏处理
        /// <summary>
        /// 处理所有分割宏操作
        /// 检查并执行各种分割宏
        /// </summary>
        private void ProcessSplitMacros()
        {
            // 二分宏处理
            if (IsKeyPressed(TwoSplitMacro.TriggerKey) && TwoSplitMacro.IsEnabled)
            {
                ExecuteSplitMacro(TwoSplitMacro);           // 执行二分宏
                WaitForKeyRelease(TwoSplitMacro.TriggerKey); // 等待按键释放
            }

            // 四分宏处理
            if (IsKeyPressed(FourSplitMacro.TriggerKey) && FourSplitMacro.IsEnabled)
            {
                ExecuteSplitMacro(FourSplitMacro);           // 执行四分宏
                WaitForKeyRelease(FourSplitMacro.TriggerKey); // 等待按键释放
            }

            // 八分宏处理
            if (IsKeyPressed(EightSplitMacro.TriggerKey) && EightSplitMacro.IsEnabled)
            {
                ExecuteSplitMacro(EightSplitMacro);           // 执行八分宏
                WaitForKeyRelease(EightSplitMacro.TriggerKey); // 等待按键释放
            }

            // 十六分宏处理
            if (IsKeyPressed(SixteenSplitMacro.TriggerKey) && SixteenSplitMacro.IsEnabled)
            {
                ExecuteSplitMacro(SixteenSplitMacro);           // 执行十六分宏
                WaitForKeyRelease(SixteenSplitMacro.TriggerKey); // 等待按键释放
            }

            // 原地宏处理
            if (IsKeyPressed(OriginMacro.TriggerKey) && OriginMacro.IsEnabled)
            {
                ExecuteOriginMacro();                       // 执行原地宏
                WaitForKeyRelease(OriginMacro.TriggerKey);  // 等待按键释放
            }
        }

        /// <summary>
        /// 执行指定配置的分割宏
        /// 根据配置参数执行分身操作
        /// </summary>
        /// <param name="config">分割宏配置</param>
        private void ExecuteSplitMacro(SplitMacroConfig config)
        {
            try
            {
                // 循环执行指定次数的分身
                for (int i = 0; i < config.NumberOfSplits; i++)
                {
                    // 第一次分身前等待延迟
                    if (i == 0)
                    {
                        Thread.Sleep(config.DelayBetweenSplits);
                    }
                    
                    // 执行分身操作
                    BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, 0);
                    
                    // 如果不是最后一次分身，等待延迟
                    if (i < config.NumberOfSplits - 1)
                    {
                        Thread.Sleep(config.DelayBetweenSplits);
                    }
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"分割宏执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行原地宏
        /// 特殊的原地宏操作，包含摇杆重置
        /// </summary>
        private void ExecuteOriginMacro()
        {
            try
            {
                // 第一次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, OriginMacro.DelayBetweenSplits);
                
                // 重置摇杆位置（这里需要根据实际的内存操作来实现）
                // neicunheqiu.writezhi(neicunheqiu.yaoganadr, 0, 0);
                Thread.Sleep(50);  // 等待摇杆重置
                
                // 第二次分身
                BallManipulationCore.SimulateKeyPress(_globalConfig.SplitKey, 0);
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"原地宏执行失败: {ex.Message}");
            }
        }
        #endregion

        #region 连续动作处理
        /// <summary>
        /// 处理所有连续动作操作
        /// 检查并执行各种连续动作
        /// </summary>
        private void ProcessContinuousActions()
        {
            // 长按宏1处理
            ProcessContinuousAction(ContinuousAction1, ref _continuousAction1Active, 
                () => new Thread(() => ContinuousActionThread(ContinuousAction1, ref _continuousAction1Active)).Start());

            // 长按宏2处理
            ProcessContinuousAction(ContinuousAction2, ref _continuousAction2Active, 
                () => new Thread(() => ContinuousActionThread(ContinuousAction2, ref _continuousAction2Active)).Start());

            // 连击宏1处理
            ProcessContinuousAction(ComboAction1, ref _comboAction1Active, 
                () => new Thread(() => ComboActionThread(ComboAction1, ref _comboAction1Active)).Start());

            // 连击宏2处理
            ProcessContinuousAction(ComboAction2, ref _comboAction2Active, 
                () => new Thread(() => ComboActionThread(ComboAction2, ref _comboAction2Active)).Start());
        }

        /// <summary>
        /// 处理单个连续动作
        /// 检查按键状态并启动相应的线程
        /// </summary>
        /// <param name="config">动作配置</param>
        /// <param name="isActive">激活状态引用</param>
        /// <param name="startAction">启动动作的委托</param>
        private void ProcessContinuousAction(ContinuousActionConfig config, ref bool isActive, Action startAction)
        {
            // 检查按键是否按下且功能启用
            if (IsKeyPressed(config.TriggerKey) && config.IsEnabled)
            {
                // 如果尚未激活，启动新线程
                if (!isActive)
                {
                    startAction();    // 启动动作线程
                    isActive = true;  // 设置激活状态
                }
            }
        }

        /// <summary>
        /// 连续动作线程（长按模式）
        /// 持续执行按键操作直到触发键释放
        /// </summary>
        /// <param name="config">动作配置</param>
        /// <param name="isActive">激活状态引用</param>
        private void ContinuousActionThread(ContinuousActionConfig config, ref bool isActive)
        {
            try
            {
                // 持续执行直到触发键释放
                while (IsKeyPressed(config.TriggerKey))
                {
                    // 执行动作按键
                    BallManipulationCore.SimulateKeyPress(config.ActionKey, 0);
                    // 等待动作间隔
                    Thread.Sleep(config.ActionInterval);
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"连续动作线程失败: {ex.Message}");
            }
            finally
            {
                // 无论如何都要重置激活状态
                isActive = false;
            }
        }

        /// <summary>
        /// 连击动作线程（重复按键模式）
        /// 快速重复按下和释放按键
        /// </summary>
        /// <param name="config">动作配置</param>
        /// <param name="isActive">激活状态引用</param>
        private void ComboActionThread(ContinuousActionConfig config, ref bool isActive)
        {
            try
            {
                // 计算按键按下和释放的时间间隔
                int halfInterval = 1000 / config.ActionInterval / 2;
                
                // 持续执行直到触发键释放
                while (IsKeyPressed(config.TriggerKey))
                {
                    // 获取动作按键的扫描码
                    uint scancode = API.MapVirtualKeyW(config.ActionKey, 0);
                    
                    // 按下动作按键
                    API.keybd_event(config.ActionKey, (byte)scancode, 0, 0);
                    Thread.Sleep(halfInterval);  // 等待半个间隔
                    
                    // 释放动作按键
                    API.keybd_event(config.ActionKey, (byte)scancode, BallManipulationCore.KEYEVENTF_KEYUP, 0);
                    Thread.Sleep(halfInterval);  // 等待半个间隔
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并记录错误信息
                System.Diagnostics.Debug.WriteLine($"连击动作线程失败: {ex.Message}");
            }
            finally
            {
                // 无论如何都要重置激活状态
                isActive = false;
            }
        }
        #endregion

        #region 工具方法
        /// <summary>
        /// 检查按键是否当前被按下
        /// </summary>
        /// <param name="keyCode">虚拟键码</param>
        /// <returns>按键是否被按下</returns>
        private bool IsKeyPressed(int keyCode)
        {
            // 使用Windows API检查按键状态
            return API.GetKeyState(keyCode) < 0;
        }

        /// <summary>
        /// 等待按键释放
        /// 阻塞直到指定按键被释放
        /// </summary>
        /// <param name="keyCode">虚拟键码</param>
        private void WaitForKeyRelease(int keyCode)
        {
            // 循环检查直到按键释放
            while (IsKeyPressed(keyCode))
            {
                Thread.Sleep(5);  // 短暂等待避免CPU占用过高
            }
        }

        /// <summary>
        /// 重置所有激活状态
        /// 停止所有正在运行的连续动作
        /// </summary>
        public void ResetAllStates()
        {
            _continuousAction1Active = false;  // 重置长按宏1状态
            _continuousAction2Active = false;  // 重置长按宏2状态
            _comboAction1Active = false;       // 重置连击宏1状态
            _comboAction2Active = false;       // 重置连击宏2状态
        }

        /// <summary>
        /// 获取所有宏的状态信息
        /// 用于调试和状态监控
        /// </summary>
        /// <returns>格式化的状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"二分宏: {TwoSplitMacro.IsEnabled}\n" +
                   $"四分宏: {FourSplitMacro.IsEnabled}\n" +
                   $"八分宏: {EightSplitMacro.IsEnabled}\n" +
                   $"十六分宏: {SixteenSplitMacro.IsEnabled}\n" +
                   $"原地宏: {OriginMacro.IsEnabled}\n" +
                   $"长按宏1激活: {_continuousAction1Active}\n" +
                   $"长按宏2激活: {_continuousAction2Active}\n" +
                   $"连击宏1激活: {_comboAction1Active}\n" +
                   $"连击宏2激活: {_comboAction2Active}";
        }
        #endregion
    }
}
