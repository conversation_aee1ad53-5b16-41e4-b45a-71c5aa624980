# 🎮 球球大作战合球软件 - 编译指南

## 📋 项目概述

这是一个完整的C语言版本球球大作战合球软件，具有现代化的GUI界面和强大的合球算法。

### ✨ 主要特性

- **🖥️ 现代化GUI界面** - 完全复现截图中的界面布局
- **🎮 完整的合球算法** - 三角合球、中分合球等多种技术
- **⚙️ 配置管理系统** - INI格式配置文件支持
- **🔒 管理员权限** - 自动请求管理员权限确保功能正常
- **🧠 内存管理模块** - 预留功能，当前安全禁用
- **🔧 模块化架构** - 清晰的代码组织和职责分离

## 🛠️ 编译环境要求

### 必需软件
- **GCC编译器** (MinGW-w64 推荐)
- **Windows SDK** (用于Win32 API)
- **Make工具** (可选，用于Makefile)

### 推荐环境
- Windows 10/11
- Visual Studio Code + C/C++ 扩展
- 或者 Code::Blocks / Dev-C++

## 🚀 快速编译

### 方法1：使用编译脚本（推荐）
```bash
# 双击运行或在命令行执行
build.bat
```

### 方法2：使用Makefile
```bash
# 编译调试版本
make debug

# 编译发布版本
make release

# 清理编译文件
make clean
```

### 方法3：手动编译
```bash
# 创建构建目录
mkdir build\release

# 编译命令
gcc -Wall -Wextra -std=c99 -Isrc -O2 -DNDEBUG -DRELEASE ^
    src\main.c ^
    src\core\ball_core.c ^
    src\config\config.c ^
    src\gui\main_window.c ^
    src\gui\controls.c ^
    src\gui\events.c ^
    src\operations\triangle_merge.c ^
    src\operations\center_split.c ^
    src\memory\memory_mgr.c ^
    -o build\release\ball_merging.exe ^
    -mwindows ^
    -luser32 -lgdi32 -lkernel32 -lcomctl32 -lcomdlg32 ^
    -lole32 -loleaut32 -luuid -ladvapi32 -lshell32
```

## 📁 项目结构

```
BallMerging/
├── src/                          # 源代码
│   ├── core/                     # 核心功能
│   │   ├── ball_core.h/.c       # 数学计算、输入模拟
│   ├── config/                   # 配置管理
│   │   ├── config.h/.c          # 配置结构和管理
│   ├── gui/                      # GUI界面
│   │   ├── main_window.h/.c     # 主窗口
│   │   ├── controls.c           # 控件创建
│   │   └── events.c             # 事件处理
│   ├── operations/               # 球体操作
│   │   ├── triangle_merge.h/.c  # 三角合球
│   │   └── center_split.h/.c    # 中分合球
│   ├── memory/                   # 内存管理（预留）
│   │   └── memory_mgr.h/.c      # 内存管理器
│   └── main.c                    # 主程序入口
├── config/                       # 配置文件
│   └── ball_merging_config.ini  # 默认配置
├── resources/                    # 资源文件
│   ├── app.rc                   # Windows资源
│   └── app.manifest             # 应用程序清单
├── build.bat                    # 编译脚本
├── Makefile                     # Make配置
└── README.md                    # 项目说明
```

## 🔧 编译选项说明

### 编译标志
- `-Wall -Wextra` - 启用所有警告
- `-std=c99` - 使用C99标准
- `-Isrc` - 包含源码目录
- `-mwindows` - 编译为Windows GUI程序
- `-O2` - 优化级别2（发布版本）
- `-g` - 包含调试信息（调试版本）

### 链接库
- `user32` - 用户界面API
- `gdi32` - 图形设备接口
- `kernel32` - 内核API
- `comctl32` - 公共控件
- `comdlg32` - 公共对话框
- `ole32` - OLE API
- `oleaut32` - OLE自动化
- `uuid` - UUID支持
- `advapi32` - 高级API
- `shell32` - Shell API

## 🎯 功能特性

### 界面功能
- ✅ 运行/结束控制
- ✅ 吐球键/分身键设置
- ✅ 摇杆大小调节（滑杆+输入框）
- ✅ 延时时间设置（滑杆+输入框）
- ✅ 三角调角度（三组按键+角度+滑杆）
- ✅ 操作模式选择（直线、大炮、侧合等）
- ✅ 宏操作配置（二键一式、三键一式）

### 合球算法
- ✅ 三角合球 - 精确的三角形阵型合球
- ✅ 中分合球 - 中心分割合球技术
- 🔄 四分合球 - 快速四分割合球（待完善）
- 🔄 旋转合球 - 半旋/全旋/蛇手（待完善）
- 🔄 后仰合球 - 后仰操作（待完善）

### 配置系统
- ✅ INI格式配置文件
- ✅ 参数验证和默认值
- ✅ 实时配置保存
- ✅ 界面与配置双向同步

## 🚨 常见问题

### 编译错误
1. **找不到头文件**
   - 确保Windows SDK已安装
   - 检查GCC是否正确配置

2. **链接错误**
   - 确保所有必需的库都已链接
   - 检查库文件路径是否正确

3. **中文显示问题**
   - 确保源文件以UTF-8编码保存
   - 检查系统区域设置

### 运行问题
1. **权限不足**
   - 程序会自动请求管理员权限
   - 如果拒绝提权，某些功能可能无法使用

2. **配置文件错误**
   - 删除配置文件让程序重新生成
   - 检查配置文件格式是否正确

3. **界面显示异常**
   - 检查DPI设置
   - 确保公共控件库已正确初始化

## 🔄 开发状态

### 已完成
- ✅ 核心架构和基础功能
- ✅ GUI界面框架
- ✅ 三角合球算法
- ✅ 中分合球算法
- ✅ 配置管理系统
- ✅ 事件处理机制
- ✅ 编译配置和脚本

### 进行中
- 🔄 其他合球算法实现
- 🔄 界面细节优化
- 🔄 错误处理完善

### 计划中
- 📋 算法性能优化
- 📋 更多合球技术
- 📋 插件系统
- 📋 网络功能

## 📞 技术支持

如果在编译或使用过程中遇到问题，请检查：

1. **编译环境** - 确保所有必需软件已正确安装
2. **源代码完整性** - 确保所有源文件都存在
3. **权限设置** - 确保有足够的权限进行编译和运行
4. **系统兼容性** - 确保在支持的Windows版本上运行

## 🎉 使用说明

编译成功后：

1. **运行程序**
   ```bash
   cd build\release
   ball_merging.exe
   ```

2. **配置参数**
   - 调整摇杆大小和延时时间
   - 设置各种合球模式
   - 配置按键绑定

3. **开始使用**
   - 点击"运行"按钮启动
   - 软件将根据配置自动执行合球操作
   - 点击"结束"按钮停止

---

**注意**: 此软件仅供学习和研究使用，请遵守相关法律法规和游戏规则。
