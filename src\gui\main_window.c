/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 主窗口GUI模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 主窗口界面的具体实现
 * • 基于Win32 API的GUI实现
 * • 完全复现截图中的界面布局和功能
 * 
 * 🎯 实现特点:
 * • 现代化的界面设计
 * • 实时参数调整和反馈
 * • 完善的事件处理机制
 * • 状态同步和错误处理
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "main_window.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 全局变量
// ═══════════════════════════════════════════════════════════════════════════════════════

static WNDCLASS g_window_class = {0};      // 窗口类
static bool g_class_registered = false;    // 窗口类是否已注册

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 窗口管理函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 注册窗口类
 */
bool register_main_window_class(HINSTANCE hinstance) {
    if (g_class_registered) {
        return true;  // 已经注册过
    }
    
    // 设置窗口类属性
    g_window_class.style = CS_HREDRAW | CS_VREDRAW;
    g_window_class.lpfnWndProc = main_window_proc;
    g_window_class.cbClsExtra = 0;
    g_window_class.cbWndExtra = sizeof(MainWindowData*);  // 存储窗口数据指针
    g_window_class.hInstance = hinstance;
    g_window_class.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    g_window_class.hCursor = LoadCursor(NULL, IDC_ARROW);
    g_window_class.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
    g_window_class.lpszMenuName = NULL;
    g_window_class.lpszClassName = WINDOW_CLASS_NAME;
    
    // 注册窗口类
    if (!RegisterClass(&g_window_class)) {
        return false;
    }
    
    g_class_registered = true;
    return true;
}

/**
 * 创建主窗口
 */
MainWindowData* create_main_window(HINSTANCE hinstance, BallMergingConfiguration* config) {
    if (!hinstance || !config) {
        return NULL;
    }
    
    // 分配窗口数据结构
    MainWindowData* window_data = (MainWindowData*)calloc(1, sizeof(MainWindowData));
    if (!window_data) {
        return NULL;
    }
    
    // 初始化窗口数据
    window_data->hinstance = hinstance;
    window_data->config = config;
    window_data->is_running = false;
    window_data->is_initialized = false;
    strcpy_s(window_data->status_text, sizeof(window_data->status_text), "就绪");
    
    // 创建主窗口
    window_data->hwnd = CreateWindowEx(
        WS_EX_CONTROLPARENT,                    // 扩展样式
        WINDOW_CLASS_NAME,                      // 窗口类名
        WINDOW_TITLE,                           // 窗口标题
        WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,  // 窗口样式
        CW_USEDEFAULT, CW_USEDEFAULT,          // 位置
        WINDOW_WIDTH, WINDOW_HEIGHT,            // 大小
        NULL,                                   // 父窗口
        NULL,                                   // 菜单
        hinstance,                              // 实例句柄
        window_data                             // 创建参数
    );
    
    if (!window_data->hwnd) {
        free(window_data);
        return NULL;
    }
    
    // 将窗口数据指针存储到窗口中
    SetWindowLongPtr(window_data->hwnd, 0, (LONG_PTR)window_data);
    
    // 创建所有控件
    if (!create_all_controls(window_data)) {
        DestroyWindow(window_data->hwnd);
        free(window_data);
        return NULL;
    }
    
    // 从配置更新界面
    update_interface_from_config(window_data);
    
    window_data->is_initialized = true;
    return window_data;
}

/**
 * 销毁主窗口
 */
void destroy_main_window(MainWindowData* window_data) {
    if (!window_data) {
        return;
    }
    
    // 停止运行状态
    if (window_data->is_running) {
        window_data->is_running = false;
    }
    
    // 销毁窗口
    if (window_data->hwnd) {
        DestroyWindow(window_data->hwnd);
        window_data->hwnd = NULL;
    }
    
    // 释放内存
    free(window_data);
}

/**
 * 显示主窗口
 */
void show_main_window(MainWindowData* window_data, int show_cmd) {
    if (!window_data || !window_data->hwnd) {
        return;
    }
    
    ShowWindow(window_data->hwnd, show_cmd);
    UpdateWindow(window_data->hwnd);
}

/**
 * 主窗口消息处理函数
 */
LRESULT CALLBACK main_window_proc(HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam) {
    MainWindowData* window_data = NULL;
    
    if (msg == WM_NCCREATE) {
        // 窗口创建时，从创建参数中获取窗口数据指针
        CREATESTRUCT* cs = (CREATESTRUCT*)lparam;
        window_data = (MainWindowData*)cs->lpCreateParams;
        SetWindowLongPtr(hwnd, 0, (LONG_PTR)window_data);
    } else {
        // 从窗口中获取窗口数据指针
        window_data = (MainWindowData*)GetWindowLongPtr(hwnd, 0);
    }
    
    switch (msg) {
        case WM_CREATE:
            return 0;
            
        case WM_COMMAND: {
            if (!window_data) break;
            
            int control_id = LOWORD(wparam);
            int notification = HIWORD(wparam);
            
            if (notification == BN_CLICKED) {
                handle_button_click(window_data, control_id);
            } else if (notification == CBN_SELCHANGE) {
                handle_combobox_change(window_data, control_id, 
                    (int)SendMessage((HWND)lparam, CB_GETCURSEL, 0, 0));
            } else if (notification == EN_CHANGE) {
                char text[256];
                GetWindowTextA((HWND)lparam, text, sizeof(text));
                handle_edit_change(window_data, control_id, text);
            }
            return 0;
        }
        
        case WM_HSCROLL: {
            if (!window_data) break;
            
            HWND slider = (HWND)lparam;
            int position = (int)SendMessage(slider, TBM_GETPOS, 0, 0);
            
            // 查找滑杆对应的控件ID
            for (int i = 0; i < 3; i++) {
                if (slider == window_data->slider_angle[i]) {
                    handle_slider_change(window_data, ID_SLIDER_ANGLE_1 + i * 3, position);
                    break;
                }
            }
            
            if (slider == window_data->slider_joystick) {
                handle_slider_change(window_data, ID_SLIDER_JOYSTICK, position);
            } else if (slider == window_data->slider_delay) {
                handle_slider_change(window_data, ID_SLIDER_DELAY, position);
            }
            
            return 0;
        }
        
        case WM_NOTIFY: {
            if (!window_data) break;
            
            NMHDR* nmhdr = (NMHDR*)lparam;
            if (nmhdr->code == NM_CUSTOMDRAW) {
                // 处理滑杆的自定义绘制
                return CDRF_DODEFAULT;
            }
            return 0;
        }
        
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // 绘制分组框和标签
            HFONT font = CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"宋体");
            HFONT old_font = (HFONT)SelectObject(hdc, font);
            
            SetBkMode(hdc, TRANSPARENT);
            SetTextColor(hdc, RGB(0, 0, 0));
            
            // 绘制各个区域的标题
            TextOutA(hdc, CONTROL_AREA_X, CONTROL_AREA_Y - 20, "运行控制", 8);
            TextOutA(hdc, SETTINGS_AREA_X, SETTINGS_AREA_Y - 20, "基础设置", 8);
            TextOutA(hdc, TRIANGLE_AREA_X, TRIANGLE_AREA_Y - 20, "一键三角 可调角度", 16);
            TextOutA(hdc, MODE_AREA_X, MODE_AREA_Y - 20, "操作模式", 8);
            
            SelectObject(hdc, old_font);
            DeleteObject(font);
            
            EndPaint(hwnd, &ps);
            return 0;
        }
        
        case WM_CTLCOLORSTATIC: {
            // 设置静态控件的颜色
            HDC hdc = (HDC)wparam;
            SetBkMode(hdc, TRANSPARENT);
            return (LRESULT)GetStockObject(NULL_BRUSH);
        }
        
        case WM_CLOSE:
            if (window_data && window_data->is_running) {
                // 如果正在运行，先停止
                window_data->is_running = false;
                update_interface_state(window_data);
            }
            DestroyWindow(hwnd);
            return 0;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wparam, lparam);
    }
    
    return DefWindowProc(hwnd, msg, wparam, lparam);
}
