/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 中分合球操作模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 中分合球操作的具体实现
 * • 基于C#版本CenterSplitMerger的完整移植和优化
 * • 实现精确的中心分割合球算法
 * 
 * 🎯 算法优化:
 * • 智能分割方向选择
 * • 自适应位置修正
 * • 优化的时序控制
 * • 完善的错误恢复
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "center_split.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 内部辅助函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

static bool validate_center_split_context(const CenterSplitContext* context);
static void update_center_execution_state(CenterSplitContext* context, CenterSplitExecutionState new_state);
static bool check_center_execution_timeout(const CenterSplitContext* context, uint64_t timeout_ms);
static void log_center_split_operation(const CenterSplitContext* context, const char* message);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 中分合球核心函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 初始化中分合球上下文
 */
bool center_split_init_context(CenterSplitContext* context,
                               const CenterSplitConfig* center_config,
                               const GlobalConfig* global_config) {
    if (!context || !center_config || !global_config) {
        return false;
    }
    
    // 清零上下文
    memset(context, 0, sizeof(CenterSplitContext));
    
    // 复制配置
    memcpy(&context->config, center_config, sizeof(CenterSplitConfig));
    memcpy(&context->global_config, global_config, sizeof(GlobalConfig));
    
    // 初始化状态
    context->state = CENTER_STATE_IDLE;
    context->is_executing = false;
    context->start_time = 0;
    context->last_operation_time = 0;
    context->split_direction = CENTER_SPLIT_AUTO;
    context->correction_applied = 0;
    context->position_offset_applied = 0;
    context->splits_executed = 0;
    context->corrections_applied = 0;
    context->error_code = 0;
    
    // 清空错误信息
    memset(context->last_error, 0, sizeof(context->last_error));
    
    return true;
}

/**
 * 执行中分合球操作
 */
bool center_split_execute(CenterSplitContext* context,
                          Point mouse_pos,
                          Rectangle window_rect,
                          CenterSplitResult* result) {
    if (!context || !result) {
        return false;
    }
    
    // 初始化结果
    memset(result, 0, sizeof(CenterSplitResult));
    result->success = false;
    
    // 检查上下文有效性
    if (!validate_center_split_context(context)) {
        center_split_set_error(context, -1, "上下文验证失败");
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        return false;
    }
    
    // 检查是否已在执行
    if (context->is_executing) {
        center_split_set_error(context, -2, "中分合球正在执行中");
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        return false;
    }
    
    // 设置执行状态
    context->is_executing = true;
    context->start_time = get_current_timestamp();
    context->mouse_position = mouse_pos;
    context->window_rect = window_rect;
    update_center_execution_state(context, CENTER_STATE_PREPARING);
    
    bool execution_success = false;
    
    do {
        // 第一步：执行准备阶段
        if (!center_split_execute_preparation(context)) {
            center_split_set_error(context, -3, "准备阶段失败");
            break;
        }
        
        // 第二步：计算中心位置和分割方向
        update_center_execution_state(context, CENTER_STATE_CALCULATING);
        if (!center_split_calculate_center_position(context)) {
            center_split_set_error(context, -4, "中心位置计算失败");
            break;
        }
        
        if (!center_split_calculate_split_direction(context)) {
            center_split_set_error(context, -5, "分割方向计算失败");
            break;
        }
        
        // 第三步：执行定位操作
        update_center_execution_state(context, CENTER_STATE_POSITIONING);
        if (!center_split_execute_positioning(context)) {
            center_split_set_error(context, -6, "定位操作失败");
            break;
        }
        
        // 第四步：执行分割操作
        update_center_execution_state(context, CENTER_STATE_SPLITTING);
        if (!center_split_execute_split_operation(context)) {
            center_split_set_error(context, -7, "分割操作失败");
            break;
        }
        
        // 第五步：执行位置修正
        update_center_execution_state(context, CENTER_STATE_CORRECTING);
        if (!center_split_execute_position_correction(context)) {
            center_split_set_error(context, -8, "位置修正失败");
            break;
        }
        
        // 第六步：执行最终分身序列
        update_center_execution_state(context, CENTER_STATE_FINALIZING);
        if (!center_split_execute_final_split_sequence(context)) {
            center_split_set_error(context, -9, "最终分身序列失败");
            break;
        }
        
        // 第七步：处理吐球功能
        if (context->config.enable_ball_ejection) {
            if (!center_split_execute_ball_ejection(context)) {
                // 吐球失败不影响整体成功
                log_center_split_operation(context, "吐球操作失败，但不影响合球结果");
            }
        }
        
        execution_success = true;
        
    } while (false);
    
    // 计算执行时间
    result->execution_time = get_current_timestamp() - context->start_time;
    
    // 设置结果
    result->success = execution_success;
    result->total_splits = context->splits_executed + CENTER_SPLIT_FINAL_COUNT;
    result->corrections_used = context->corrections_applied;
    result->final_direction = context->split_direction;
    result->final_error_code = context->error_code;
    
    if (execution_success) {
        strcpy_s(result->result_message, sizeof(result->result_message), "中分合球执行成功");
        update_center_execution_state(context, CENTER_STATE_IDLE);
    } else {
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        update_center_execution_state(context, CENTER_STATE_ERROR);
    }
    
    // 重置执行状态
    context->is_executing = false;
    
    return execution_success;
}

/**
 * 停止中分合球操作
 */
bool center_split_stop(CenterSplitContext* context) {
    if (!context) {
        return false;
    }
    
    if (context->is_executing) {
        context->is_executing = false;
        update_center_execution_state(context, CENTER_STATE_IDLE);
        center_split_set_error(context, 0, "操作已被用户停止");
        return true;
    }
    
    return false;
}

/**
 * 重置中分合球上下文
 */
void center_split_reset_context(CenterSplitContext* context) {
    if (!context) {
        return;
    }
    
    // 保存配置
    CenterSplitConfig saved_config = context->config;
    GlobalConfig saved_global_config = context->global_config;
    
    // 清零上下文
    memset(context, 0, sizeof(CenterSplitContext));
    
    // 恢复配置
    context->config = saved_config;
    context->global_config = saved_global_config;
    
    // 重置状态
    context->state = CENTER_STATE_IDLE;
    context->is_executing = false;
    context->split_direction = CENTER_SPLIT_AUTO;
}

/**
 * 销毁中分合球上下文
 */
void center_split_destroy_context(CenterSplitContext* context) {
    if (!context) {
        return;
    }
    
    // 如果正在执行，先停止
    if (context->is_executing) {
        center_split_stop(context);
    }
    
    // 清零上下文
    memset(context, 0, sizeof(CenterSplitContext));
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 中分合球计算函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算中心位置
 */
bool center_split_calculate_center_position(CenterSplitContext* context) {
    if (!context) {
        return false;
    }
    
    // 计算窗口中心点
    context->center_position.x = (context->window_rect.left + context->window_rect.right) / 2;
    context->center_position.y = (context->window_rect.top + context->window_rect.bottom) / 2;
    
    // 应用位置偏移
    context->center_position.x += context->config.position_offset;
    context->center_position.y += context->config.position_offset;
    
    // 确保位置在窗口范围内
    context->center_position.x = clamp_value(context->center_position.x,
                                            context->window_rect.left + 50,
                                            context->window_rect.right - 50);
    context->center_position.y = clamp_value(context->center_position.y,
                                            context->window_rect.top + 50,
                                            context->window_rect.bottom - 50);
    
    // 初始化修正后位置
    context->corrected_position = context->center_position;
    
    return true;
}

/**
 * 计算分割方向
 */
bool center_split_calculate_split_direction(CenterSplitContext* context) {
    if (!context) {
        return false;
    }
    
    if (context->config.use_vertical_split) {
        context->split_direction = CENTER_SPLIT_VERTICAL;
    } else {
        // 根据鼠标位置和窗口中心的关系自动选择方向
        double dx = context->mouse_position.x - context->center_position.x;
        double dy = context->mouse_position.y - context->center_position.y;
        
        if (abs((int)dx) > abs((int)dy)) {
            context->split_direction = CENTER_SPLIT_HORIZONTAL;
        } else {
            context->split_direction = CENTER_SPLIT_VERTICAL;
        }
    }
    
    return true;
}

/**
 * 应用位置修正
 */
bool center_split_apply_position_correction(CenterSplitContext* context) {
    if (!context) {
        return false;
    }
    
    // 应用修正值
    int correction = context->config.correction_value;
    if (correction != 0) {
        context->corrected_position.x = context->center_position.x + correction;
        context->corrected_position.y = context->center_position.y + correction;
        
        // 确保修正后位置在有效范围内
        context->corrected_position.x = clamp_value(context->corrected_position.x,
                                                   context->window_rect.left,
                                                   context->window_rect.right);
        context->corrected_position.y = clamp_value(context->corrected_position.y,
                                                   context->window_rect.top,
                                                   context->window_rect.bottom);
        
        context->correction_applied = correction;
        context->corrections_applied++;
    }
    
    return true;
}

/**
 * 验证中分参数
 */
bool center_split_validate_parameters(CenterSplitContext* context) {
    if (!context) {
        return false;
    }
    
    // 验证延迟时间
    if (context->config.initial_delay < 0 || context->config.initial_delay > 5000 ||
        context->config.split_delay < 0 || context->config.split_delay > 5000 ||
        context->config.final_split_delay < 0 || context->config.final_split_delay > 5000) {
        center_split_set_error(context, -10, "延迟时间超出有效范围");
        return false;
    }
    
    // 验证修正值
    if (abs(context->config.correction_value) > CENTER_SPLIT_MAX_CORRECTION) {
        center_split_set_error(context, -11, "修正值超出有效范围");
        return false;
    }
    
    // 验证位置偏移
    if (context->config.position_offset < CENTER_SPLIT_MIN_OFFSET ||
        context->config.position_offset > CENTER_SPLIT_MAX_OFFSET) {
        center_split_set_error(context, -12, "位置偏移超出有效范围");
        return false;
    }
    
    return true;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 中分合球执行函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行准备阶段
 */
bool center_split_execute_preparation(CenterSplitContext* context) {
    if (!context) {
        return false;
    }

    // 验证参数
    if (!center_split_validate_parameters(context)) {
        return false;
    }

    // 初始延迟
    if (context->config.initial_delay > 0) {
        precise_delay(context->config.initial_delay);
    }

    // 如果启用吐球功能，按下吐球键
    if (context->config.enable_ball_ejection) {
        simulate_key_press(context->global_config.ball_eject_key, 0);
    }

    return true;
}

/**
 * 执行定位操作
 */
bool center_split_execute_positioning(CenterSplitContext* context) {
    if (!context) {
        return false;
    }

    // 移动鼠标到中心位置
    if (!simulate_mouse_move(context->center_position.x, context->center_position.y)) {
        center_split_set_error(context, -20, "鼠标移动到中心位置失败");
        return false;
    }

    // 短暂延迟确保移动完成
    precise_delay(20);

    return true;
}

/**
 * 执行分割操作
 */
bool center_split_execute_split_operation(CenterSplitContext* context) {
    if (!context) {
        return false;
    }

    // 执行第一次分身
    if (!simulate_key_press(context->global_config.split_key, context->config.split_delay)) {
        center_split_set_error(context, -21, "第一次分身操作失败");
        return false;
    }
    context->splits_executed++;

    // 根据分割方向进行微调
    Point adjustment_pos = context->center_position;

    switch (context->split_direction) {
        case CENTER_SPLIT_HORIZONTAL:
            // 水平分割：稍微向左移动
            adjustment_pos.x -= 10;
            break;
        case CENTER_SPLIT_VERTICAL:
            // 垂直分割：稍微向上移动
            adjustment_pos.y -= 10;
            break;
        case CENTER_SPLIT_AUTO:
            // 自动模式：根据鼠标位置决定
            if (context->mouse_position.x < context->center_position.x) {
                adjustment_pos.x -= 10;
            } else {
                adjustment_pos.y -= 10;
            }
            break;
    }

    // 移动到调整位置
    if (!simulate_mouse_move(adjustment_pos.x, adjustment_pos.y)) {
        center_split_set_error(context, -22, "移动到调整位置失败");
        return false;
    }

    // 短暂延迟
    precise_delay(10);

    // 执行第二次分身
    if (!simulate_key_press(context->global_config.split_key, context->config.split_delay)) {
        center_split_set_error(context, -23, "第二次分身操作失败");
        return false;
    }
    context->splits_executed++;

    // 更新最后操作时间
    context->last_operation_time = get_current_timestamp();

    return true;
}

/**
 * 执行位置修正
 */
bool center_split_execute_position_correction(CenterSplitContext* context) {
    if (!context) {
        return false;
    }

    // 应用位置修正
    if (!center_split_apply_position_correction(context)) {
        return false;
    }

    // 如果有修正，移动到修正位置
    if (context->correction_applied != 0) {
        if (!simulate_mouse_move(context->corrected_position.x, context->corrected_position.y)) {
            center_split_set_error(context, -24, "移动到修正位置失败");
            return false;
        }

        // 短暂延迟
        precise_delay(15);

        // 执行修正分身
        if (!simulate_key_press(context->global_config.split_key, context->config.split_delay)) {
            center_split_set_error(context, -25, "修正分身操作失败");
            return false;
        }
        context->splits_executed++;
    }

    return true;
}

/**
 * 执行最终分身序列
 */
bool center_split_execute_final_split_sequence(CenterSplitContext* context) {
    if (!context) {
        return false;
    }

    // 移动回中心位置
    if (!simulate_mouse_move(context->center_position.x, context->center_position.y)) {
        center_split_set_error(context, -26, "移动回中心位置失败");
        return false;
    }

    // 短暂延迟
    precise_delay(10);

    // 执行多次分身操作，确保球体完全合并
    for (int i = 0; i < CENTER_SPLIT_FINAL_COUNT; i++) {
        if (!simulate_key_press(context->global_config.split_key, context->config.final_split_delay)) {
            center_split_set_error(context, -27, "最终分身序列失败");
            return false;
        }

        // 检查是否被停止
        if (!context->is_executing) {
            return false;
        }
    }

    return true;
}
