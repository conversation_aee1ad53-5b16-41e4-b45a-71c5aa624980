# ═══════════════════════════════════════════════════════════════════════════════════════
# 🎮 球球大作战合球软件配置文件
# ═══════════════════════════════════════════════════════════════════════════════════════
# 
# 📋 配置说明:
# • 此文件包含软件的所有配置参数
# • 修改后需要重启软件才能生效
# • 参数值请在有效范围内设置
# • 使用 # 开头的行为注释
# 
# 🎯 配置分类:
# • [Global] - 全局设置
# • [TriangleMerging1] - 三角合球1配置
# • [TriangleMerging2] - 三角合球2配置
# • [CenterSplit] - 中分配置
# • [QuarterSplit] - 四分配置
# • [BackwardLean] - 后仰配置
# • [LeverMacro] - 杠杆宏配置
# • [Rotation] - 旋转配置
# • [Macros] - 宏操作配置
# ═══════════════════════════════════════════════════════════════════════════════════════

# 版本信息
ConfigVersion=1.2.0
LastModified=1703001600

[Global]
# 全局设置
SplitKey=69                    # 分身键 (E键=69)
BallEjectKey=81                # 吐球键 (Q键=81)
JoystickSensitivity=100        # 摇杆灵敏度 (1-200)
WindowTitle=球球大作战          # 目标窗口标题
AutoDetectWindow=1             # 自动检测窗口 (0=否, 1=是)
BaseDelay=100                  # 基础延迟时间 (毫秒)
EnableDebugMode=0              # 启用调试模式 (0=否, 1=是)

[TriangleMerging1]
# 三角合球1配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=49                  # 触发按键 (1键=49)
FormationAngle=60              # 阵型角度 (度)
InitialDelay=50                # 初始延迟 (毫秒)
SplitDelay=100                 # 分身延迟 (毫秒)
FinalSplitDelay=50             # 最终分身延迟 (毫秒)
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
UseArrowDirection=0            # 使用箭头方向 (0=否, 1=是)
TriangleRadius=50              # 三角形半径
CorrectionOffset=0             # 修正偏移量

[TriangleMerging2]
# 三角合球2配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=50                  # 触发按键 (2键=50)
FormationAngle=90              # 阵型角度 (度)
InitialDelay=50                # 初始延迟 (毫秒)
SplitDelay=100                 # 分身延迟 (毫秒)
FinalSplitDelay=50             # 最终分身延迟 (毫秒)
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
UseArrowDirection=0            # 使用箭头方向 (0=否, 1=是)
TriangleRadius=50              # 三角形半径
CorrectionOffset=0             # 修正偏移量

[CenterSplit]
# 中分配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=81                  # 触发按键 (Q键=81)
InitialDelay=50                # 初始延迟 (毫秒)
SplitDelay=100                 # 分身延迟 (毫秒)
FinalSplitDelay=50             # 最终分身延迟 (毫秒)
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
UseVerticalSplit=0             # 使用垂直分割 (0=否, 1=是)
CorrectionValue=0              # 修正值
PositionOffset=0               # 位置偏移

[QuarterSplit]
# 四分配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=87                  # 触发按键 (W键=87)
FirstSplitDelay=50             # 第一次分身延迟 (毫秒)
SecondSplitDelay=100           # 第二次分身延迟 (毫秒)
FinalSplitDelay=50             # 最终分身延迟 (毫秒)
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
PresetMode=2                   # 预设模式 (0=快速, 1=精确, 2=平衡)
EnableValidation=1             # 启用参数验证 (0=否, 1=是)

[BackwardLean]
# 后仰配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=85                  # 触发按键 (U键=85)
LeanAngle=180                  # 后仰角度 (度)
LeanDistance=50                # 后仰距离
ExecutionDelay=100             # 执行延迟 (毫秒)
UseDynamicAngle=0              # 使用动态角度 (0=否, 1=是)
DirectionCorrection=0          # 方向修正值

[LeverMacro]
# 杠杆宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=76                  # 触发按键 (L键=76)
DoubleClickThreshold=300       # 双击检测阈值 (毫秒)
ExecutionDelay=100             # 执行延迟 (毫秒)
EnablePartialExecution=0       # 启用部分执行 (0=否, 1=是)
TimeWindow=500                 # 时间窗口 (毫秒)
EnableStateManagement=1        # 启用状态管理 (0=否, 1=是)

[HalfRotation]
# 半旋配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=52                  # 触发按键 (4键=52)
RotationAngle=180              # 旋转角度 (度)
RotationRadius=50              # 旋转半径
StepDelay=50                   # 步骤延迟 (毫秒)
RotationSteps=4                # 旋转步数
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
DirectionMode=0                # 方向模式 (0=自动, 1=顺时针, 2=逆时针)
RadiusScaling1=100             # 半径缩放1
RadiusScaling2=80              # 半径缩放2
RadiusScaling3=60              # 半径缩放3
RadiusScaling4=40              # 半径缩放4

[FullRotation]
# 全旋配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=53                  # 触发按键 (5键=53)
RotationAngle=360              # 旋转角度 (度)
RotationRadius=50              # 旋转半径
StepDelay=50                   # 步骤延迟 (毫秒)
RotationSteps=4                # 旋转步数
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
DirectionMode=0                # 方向模式 (0=自动, 1=顺时针, 2=逆时针)
RadiusScaling1=100             # 半径缩放1
RadiusScaling2=80              # 半径缩放2
RadiusScaling3=60              # 半径缩放3
RadiusScaling4=40              # 半径缩放4

[SnakeHand]
# 蛇手配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=54                  # 触发按键 (6键=54)
RotationAngle=90               # 旋转角度 (度)
RotationRadius=50              # 旋转半径
StepDelay=50                   # 步骤延迟 (毫秒)
RotationSteps=4                # 旋转步数
EnableBallEjection=0           # 启用吐球 (0=否, 1=是)
DirectionMode=0                # 方向模式 (0=自动, 1=顺时针, 2=逆时针)
RadiusScaling1=100             # 半径缩放1
RadiusScaling2=80              # 半径缩放2
RadiusScaling3=60              # 半径缩放3
RadiusScaling4=40              # 半径缩放4

[SplitMacro2]
# 二分宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=50                  # 触发按键 (2键=50)
SplitCount=2                   # 分身次数
DelayBetweenSplits=50          # 分身间隔 (毫秒)
InitialDelay=0                 # 初始延迟 (毫秒)
EnableValidation=1             # 启用验证 (0=否, 1=是)

[SplitMacro4]
# 四分宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=52                  # 触发按键 (4键=52)
SplitCount=4                   # 分身次数
DelayBetweenSplits=50          # 分身间隔 (毫秒)
InitialDelay=0                 # 初始延迟 (毫秒)
EnableValidation=1             # 启用验证 (0=否, 1=是)

[SplitMacro8]
# 八分宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=56                  # 触发按键 (8键=56)
SplitCount=8                   # 分身次数
DelayBetweenSplits=50          # 分身间隔 (毫秒)
InitialDelay=0                 # 初始延迟 (毫秒)
EnableValidation=1             # 启用验证 (0=否, 1=是)

[SplitMacro16]
# 十六分宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=54                  # 触发按键 (6键=54)
SplitCount=16                  # 分身次数
DelayBetweenSplits=50          # 分身间隔 (毫秒)
InitialDelay=0                 # 初始延迟 (毫秒)
EnableValidation=1             # 启用验证 (0=否, 1=是)

[OriginMacro]
# 原地宏配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=48                  # 触发按键 (0键=48)
SplitCount=2                   # 分身次数
DelayBetweenSplits=50          # 分身间隔 (毫秒)
InitialDelay=0                 # 初始延迟 (毫秒)
EnableValidation=1             # 启用验证 (0=否, 1=是)

[ContinuousAction1]
# 长按宏1配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=90                  # 触发按键 (Z键=90)
ActionKey=88                   # 动作按键 (X键=88)
ActionInterval=100             # 动作间隔 (毫秒)
MaxDuration=5000               # 最大持续时间 (毫秒)
IsContinuous=1                 # 是否连续执行 (0=否, 1=是)
RepeatCount=10                 # 重复次数

[ContinuousAction2]
# 长按宏2配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=67                  # 触发按键 (C键=67)
ActionKey=86                   # 动作按键 (V键=86)
ActionInterval=100             # 动作间隔 (毫秒)
MaxDuration=5000               # 最大持续时间 (毫秒)
IsContinuous=1                 # 是否连续执行 (0=否, 1=是)
RepeatCount=10                 # 重复次数

[ComboAction1]
# 连击宏1配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=66                  # 触发按键 (B键=66)
ActionKey=78                   # 动作按键 (N键=78)
ActionInterval=100             # 动作间隔 (毫秒)
MaxDuration=5000               # 最大持续时间 (毫秒)
IsContinuous=0                 # 是否连续执行 (0=否, 1=是)
RepeatCount=10                 # 重复次数

[ComboAction2]
# 连击宏2配置
IsEnabled=0                    # 是否启用 (0=否, 1=是)
TriggerKey=77                  # 触发按键 (M键=77)
ActionKey=32                   # 动作按键 (空格键=32)
ActionInterval=100             # 动作间隔 (毫秒)
MaxDuration=5000               # 最大持续时间 (毫秒)
IsContinuous=0                 # 是否连续执行 (0=否, 1=是)
RepeatCount=10                 # 重复次数
