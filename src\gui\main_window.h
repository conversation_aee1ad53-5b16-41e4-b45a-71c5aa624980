/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 主窗口GUI模块头文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 主窗口界面的定义和声明
 * • 基于Win32 API的GUI实现
 * • 复现截图中的界面布局和功能
 * 
 * 🎯 界面功能:
 * • 🎮 运行控制区域（运行/结束按钮）
 * • ⚙️ 基础设置区域（吐球键、分身键、摇杆大小、延时时间）
 * • 🔧 一键三角调角度区域（按键号、角度、拖动滑杆改变角度）
 * • 🎯 操作模式区域（直线、大炮、侧合为U、侧合左I等）
 * • 🤖 宏操作区域（二键一式、三键一式的各种组合）
 * 
 * ✨ 设计特点:
 * • 现代化的界面设计
 * • 实时参数调整
 * • 状态指示和反馈
 * • 快捷键支持
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <windows.h>
#include <commctrl.h>
#include <stdbool.h>
#include "../config/config.h"

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 窗口常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define WINDOW_CLASS_NAME L"BallMergingMainWindow"
#define WINDOW_TITLE L"球球大/作战合球整版1.2 [杜杆发射]"
#define WINDOW_WIDTH 850
#define WINDOW_HEIGHT 600

// 控件ID定义
#define ID_BTN_START 1001                  // 运行按钮
#define ID_BTN_STOP 1002                   // 结束按钮
#define ID_COMBO_EJECT_KEY 1003            // 吐球键下拉框
#define ID_COMBO_SPLIT_KEY 1004            // 分身键下拉框
#define ID_EDIT_JOYSTICK_SIZE 1005         // 摇杆大小输入框
#define ID_SLIDER_JOYSTICK 1006            // 摇杆大小滑杆
#define ID_EDIT_DELAY_TIME 1007            // 延时时间输入框
#define ID_SLIDER_DELAY 1008               // 延时时间滑杆

// 一键三角调角度区域
#define ID_COMBO_KEY_1 1010                // 按键号1下拉框
#define ID_EDIT_ANGLE_1 1011               // 角度1输入框
#define ID_SLIDER_ANGLE_1 1012             // 角度1滑杆
#define ID_COMBO_KEY_2 1013                // 按键号2下拉框
#define ID_EDIT_ANGLE_2 1014               // 角度2输入框
#define ID_SLIDER_ANGLE_2 1015             // 角度2滑杆
#define ID_COMBO_KEY_3 1016                // 按键号3下拉框
#define ID_EDIT_ANGLE_3 1017               // 角度3输入框
#define ID_SLIDER_ANGLE_3 1018             // 角度3滑杆

// 操作模式复选框
#define ID_CHECK_AUTO_MERGE 1020           // 合球时自动吐
#define ID_CHECK_STRAIGHT_LINE 1021        // 直线
#define ID_CHECK_CANNON 1022               // 大炮
#define ID_CHECK_SIDE_MERGE_U 1023         // 侧合为U
#define ID_CHECK_SIDE_MERGE_I 1024         // 侧合左I

// 二键一式区域
#define ID_CHECK_SIDE_4 1030               // 侧合4
#define ID_CHECK_QUARTER_5 1031            // 四分5
#define ID_CHECK_ROTATION_6 1032           // 旋转6
#define ID_CHECK_BACKWARD_C 1033           // 后仰C

// 三键一式区域
#define ID_CHECK_CENTER_Q 1040             // 中分寸Q
#define ID_CHECK_QUARTER_W 1041            // 四分寸W
#define ID_CHECK_IMMORTAL_E 1042           // 仙人指E

// 下拉框选项
#define ID_COMBO_STRAIGHT_OPT 1050         // 直线选项
#define ID_COMBO_CANNON_OPT 1051           // 大炮选项
#define ID_COMBO_SIDE_U_OPT 1052           // 侧合U选项
#define ID_COMBO_SIDE_I_OPT 1053           // 侧合I选项
#define ID_COMBO_SIDE_4_OPT 1054           // 侧合4选项
#define ID_COMBO_QUARTER_5_OPT 1055        // 四分5选项
#define ID_COMBO_ROTATION_6_OPT 1056       // 旋转6选项
#define ID_COMBO_BACKWARD_C_OPT 1057       // 后仰C选项
#define ID_COMBO_CENTER_Q_OPT 1058         // 中分Q选项
#define ID_COMBO_QUARTER_W_OPT 1059        // 四分W选项
#define ID_COMBO_IMMORTAL_E_OPT 1060       // 仙人指E选项

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 窗口数据结构
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 🎮 主窗口数据结构
 * 包含窗口状态和控件句柄
 */
typedef struct {
    HWND hwnd;                             // 主窗口句柄
    HINSTANCE hinstance;                   // 应用程序实例
    BallMergingConfiguration* config;      // 配置数据指针
    
    // 运行状态
    bool is_running;                       // 是否正在运行
    bool is_initialized;                   // 是否已初始化
    
    // 控件句柄
    HWND btn_start;                        // 运行按钮
    HWND btn_stop;                         // 结束按钮
    HWND combo_eject_key;                  // 吐球键下拉框
    HWND combo_split_key;                  // 分身键下拉框
    HWND edit_joystick_size;               // 摇杆大小输入框
    HWND slider_joystick;                  // 摇杆大小滑杆
    HWND edit_delay_time;                  // 延时时间输入框
    HWND slider_delay;                     // 延时时间滑杆
    
    // 一键三角调角度控件
    HWND combo_key[3];                     // 按键号下拉框数组
    HWND edit_angle[3];                    // 角度输入框数组
    HWND slider_angle[3];                  // 角度滑杆数组
    
    // 操作模式复选框
    HWND check_auto_merge;                 // 合球时自动吐
    HWND check_straight_line;              // 直线
    HWND check_cannon;                     // 大炮
    HWND check_side_merge_u;               // 侧合为U
    HWND check_side_merge_i;               // 侧合左I
    
    // 二键一式复选框
    HWND check_side_4;                     // 侧合4
    HWND check_quarter_5;                  // 四分5
    HWND check_rotation_6;                 // 旋转6
    HWND check_backward_c;                 // 后仰C
    
    // 三键一式复选框
    HWND check_center_q;                   // 中分寸Q
    HWND check_quarter_w;                  // 四分寸W
    HWND check_immortal_e;                 // 仙人指E
    
    // 下拉框选项
    HWND combo_options[12];                // 各种选项下拉框数组
    
    // 状态信息
    HWND status_bar;                       // 状态栏
    char status_text[256];                 // 状态文本
} MainWindowData;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 窗口管理函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 注册窗口类
 * @param hinstance 应用程序实例
 * @return 是否注册成功
 */
bool register_main_window_class(HINSTANCE hinstance);

/**
 * 创建主窗口
 * @param hinstance 应用程序实例
 * @param config 配置数据指针
 * @return 窗口数据结构指针
 */
MainWindowData* create_main_window(HINSTANCE hinstance, BallMergingConfiguration* config);

/**
 * 销毁主窗口
 * @param window_data 窗口数据指针
 */
void destroy_main_window(MainWindowData* window_data);

/**
 * 显示主窗口
 * @param window_data 窗口数据指针
 * @param show_cmd 显示命令
 */
void show_main_window(MainWindowData* window_data, int show_cmd);

/**
 * 主窗口消息处理函数
 * @param hwnd 窗口句柄
 * @param msg 消息类型
 * @param wparam 消息参数1
 * @param lparam 消息参数2
 * @return 消息处理结果
 */
LRESULT CALLBACK main_window_proc(HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 界面创建函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 创建所有控件
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_all_controls(MainWindowData* window_data);

/**
 * 创建运行控制区域
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_control_area(MainWindowData* window_data);

/**
 * 创建基础设置区域
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_basic_settings_area(MainWindowData* window_data);

/**
 * 创建一键三角调角度区域
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_triangle_angle_area(MainWindowData* window_data);

/**
 * 创建操作模式区域
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_operation_mode_area(MainWindowData* window_data);

/**
 * 创建宏操作区域
 * @param window_data 窗口数据指针
 * @return 是否创建成功
 */
bool create_macro_operation_area(MainWindowData* window_data);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 事件处理函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 处理按钮点击事件
 * @param window_data 窗口数据指针
 * @param control_id 控件ID
 */
void handle_button_click(MainWindowData* window_data, int control_id);

/**
 * 处理滑杆变化事件
 * @param window_data 窗口数据指针
 * @param control_id 控件ID
 * @param position 滑杆位置
 */
void handle_slider_change(MainWindowData* window_data, int control_id, int position);

/**
 * 处理复选框状态变化
 * @param window_data 窗口数据指针
 * @param control_id 控件ID
 * @param is_checked 是否选中
 */
void handle_checkbox_change(MainWindowData* window_data, int control_id, bool is_checked);

/**
 * 处理下拉框选择变化
 * @param window_data 窗口数据指针
 * @param control_id 控件ID
 * @param selection 选择项索引
 */
void handle_combobox_change(MainWindowData* window_data, int control_id, int selection);

/**
 * 处理文本框内容变化
 * @param window_data 窗口数据指针
 * @param control_id 控件ID
 * @param text 文本内容
 */
void handle_edit_change(MainWindowData* window_data, int control_id, const char* text);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 界面更新函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 更新界面状态
 * @param window_data 窗口数据指针
 */
void update_interface_state(MainWindowData* window_data);

/**
 * 更新状态栏信息
 * @param window_data 窗口数据指针
 * @param message 状态消息
 */
void update_status_bar(MainWindowData* window_data, const char* message);

/**
 * 从配置更新界面
 * @param window_data 窗口数据指针
 */
void update_interface_from_config(MainWindowData* window_data);

/**
 * 从界面更新配置
 * @param window_data 窗口数据指针
 */
void update_config_from_interface(MainWindowData* window_data);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 界面布局常量
// ═══════════════════════════════════════════════════════════════════════════════════════

// 控件位置和大小定义
#define CONTROL_MARGIN 10                  // 控件边距
#define BUTTON_WIDTH 80                    // 按钮宽度
#define BUTTON_HEIGHT 30                   // 按钮高度
#define COMBO_WIDTH 120                    // 下拉框宽度
#define COMBO_HEIGHT 25                    // 下拉框高度
#define EDIT_WIDTH 80                      // 输入框宽度
#define EDIT_HEIGHT 25                     // 输入框高度
#define SLIDER_WIDTH 150                   // 滑杆宽度
#define SLIDER_HEIGHT 25                   // 滑杆高度
#define CHECKBOX_WIDTH 100                 // 复选框宽度
#define CHECKBOX_HEIGHT 20                 // 复选框高度
#define LABEL_HEIGHT 20                    // 标签高度

// 区域位置定义
#define CONTROL_AREA_X 10                  // 控制区域X位置
#define CONTROL_AREA_Y 10                  // 控制区域Y位置
#define CONTROL_AREA_WIDTH 200             // 控制区域宽度
#define CONTROL_AREA_HEIGHT 100            // 控制区域高度

#define SETTINGS_AREA_X 10                 // 设置区域X位置
#define SETTINGS_AREA_Y 120                // 设置区域Y位置
#define SETTINGS_AREA_WIDTH 400            // 设置区域宽度
#define SETTINGS_AREA_HEIGHT 150           // 设置区域高度

#define TRIANGLE_AREA_X 10                 // 三角区域X位置
#define TRIANGLE_AREA_Y 280                // 三角区域Y位置
#define TRIANGLE_AREA_WIDTH 400            // 三角区域宽度
#define TRIANGLE_AREA_HEIGHT 120           // 三角区域高度

#define MODE_AREA_X 420                    // 模式区域X位置
#define MODE_AREA_Y 10                     // 模式区域Y位置
#define MODE_AREA_WIDTH 400                // 模式区域宽度
#define MODE_AREA_HEIGHT 500               // 模式区域高度

#endif // MAIN_WINDOW_H
