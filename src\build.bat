@echo off
REM ═══════════════════════════════════════════════════════════════════════════════════════
REM 🎮 球球大作战合球软件 - Windows编译脚本
REM ═══════════════════════════════════════════════════════════════════════════════════════

echo 🎮 球球大作战合球软件编译脚本
echo ═══════════════════════════════════════════════════════════════════════════════════════

REM 创建构建目录
if not exist "build" mkdir build
if not exist "build\debug" mkdir build\debug
if not exist "build\release" mkdir build\release

REM 设置编译器和参数
set CC=gcc
set CFLAGS=-Wall -Wextra -std=c99 -Isrc
set LIBS=-luser32 -lgdi32 -lkernel32 -lcomctl32 -lcomdlg32 -lole32 -loleaut32 -luuid -ladvapi32 -lshell32
set LDFLAGS=-mwindows

REM 源文件列表
set SOURCES=src\main.c src\core\ball_core.c src\config\config.c src\gui\main_window.c src\gui\controls.c src\gui\events.c src\operations\triangle_merge.c src\operations\center_split.c src\memory\memory_mgr.c

echo 🔨 开始编译...
echo.

REM 编译调试版本
echo 📋 编译调试版本...
%CC% %CFLAGS% -g -O0 -DDEBUG %SOURCES% -o build\debug\ball_merging.exe %LDFLAGS% %LIBS%

if %ERRORLEVEL% EQU 0 (
    echo ✅ 调试版本编译成功: build\debug\ball_merging.exe
) else (
    echo ❌ 调试版本编译失败
    goto :error
)

echo.

REM 编译发布版本
echo 📋 编译发布版本...
%CC% %CFLAGS% -O2 -DNDEBUG -DRELEASE %SOURCES% -o build\release\ball_merging.exe %LDFLAGS% %LIBS%

if %ERRORLEVEL% EQU 0 (
    echo ✅ 发布版本编译成功: build\release\ball_merging.exe
) else (
    echo ❌ 发布版本编译失败
    goto :error
)

echo.

REM 复制配置文件
echo 📦 复制配置文件...
if exist "config" (
    xcopy /E /I /Y "config" "build\release\config" >nul
    echo ✅ 配置文件复制完成
)

echo.
echo 🎉 编译完成！
echo.
echo 📁 输出文件:
echo    调试版本: build\debug\ball_merging.exe
echo    发布版本: build\release\ball_merging.exe
echo.
echo 🚀 运行程序:
echo    cd build\release
echo    ball_merging.exe
echo.

goto :end

:error
echo.
echo ❌ 编译失败！请检查错误信息。
echo.
echo 💡 常见问题解决方案:
echo    1. 确保已安装GCC编译器
echo    2. 检查源文件是否存在
echo    3. 确保Windows SDK已正确安装
echo    4. 检查路径中是否包含中文字符
echo.
pause
exit /b 1

:end
pause
