/*
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * ⚙️ 球体合并配置管理类 (BallMergingConfiguration.cs)
 * ═══════════════════════════════════════════════════════════════════════════════════════
 *
 * 📋 功能说明:
 * ┌─────────────────────────────────────────────────────────────────────────────────────┐
 * │ 这是整个球体合并系统的配置中心，集中管理所有操作的参数设置：                          │
 * │                                                                                     │
 * │ 🎯 球体操作配置:                                                                    │
 * │   • TriangleMergingConfig - 三角合球配置（角度、半径、时间延迟等）                   │
 * │   • CenterSplitConfig - 中分配置（拖拽延迟、分身时机等）                            │
 * │   • QuarterSplitConfig - 四分配置（分身序列、移动参数等）                           │
 * │   • BackwardLeanConfig - 后仰配置（后仰角度、移动距离等）                           │
 * │   • RotationConfig - 旋转配置（4步角度、半径、时间序列等）                          │
 * │                                                                                     │
 * │ 🤖 宏操作配置:                                                                      │
 * │   • LeverMacroConfig - 杠杆宏配置（双击检测、时间窗口等）                           │
 * │   • SplitMacroConfig - 分割宏配置（分身次数、延迟时间等）                           │
 * │   • ContinuousActionConfig - 连续动作配置（按键间隔、动作类型等）                   │
 * │                                                                                     │
 * │ 🌐 全局配置:                                                                        │
 * │   • GlobalConfig - 全局设置（分身键、吐球键、摇杆灵敏度、窗口句柄等）               │
 * └─────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎯 设计特点:
 * • 类型安全 - 每个配置都有明确的数据类型，避免配置错误
 * • 默认值管理 - 每个配置类都提供合理的默认值
 * • 分类组织 - 按功能模块分类，便于管理和查找
 * • 验证支持 - 支持参数有效性验证
 * • 序列化友好 - 可以轻松保存到文件或数据库
 *
 * 🔗 使用场景:
 * • 游戏启动时加载配置
 * • 用户界面参数调整
 * • 配置文件保存/加载
 * • 不同场景的配置切换
 * • 参数优化和调试
 *
 * 📊 配置层次结构:
 * BallMergingConfiguration (主配置)
 * ├── TriangleMerging/TriangleMerging2 (三角合球1/2)
 * ├── CenterSplit (中分)
 * ├── QuarterSplit (四分)
 * ├── BackwardLean (后仰)
 * ├── LeverMacro (杠杆宏)
 * ├── HalfRotation/FullRotation/SnakeHand (旋转操作)
 * └── Global (全局设置)
 *
 * ⚠️ 重要提示:
 * 修改配置结构时需要同步更新所有使用该配置的操作类
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

using System;

namespace ANYE_Balls.BallMerging.Configuration
{
    /// <summary>
    /// 球体合并配置类 - 集中管理所有球体操作的参数设置
    /// 这个类包含了所有球体合并技术的配置参数
    /// </summary>
    public class BallMergingConfiguration
    {
        #region 三角合球配置类
        
        /// <summary>
        /// 三角合球操作的配置类
        /// 用于配置三角形阵型的球体合并参数
        /// </summary>
        public class TriangleMergingConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用三角合球功能
            public bool UseArrowDirection { get; set; }      // 是否使用箭头方向（鼠标拖拽方向）
            public bool EnableBallEjection { get; set; }     // 是否启用吐球功能
            public int TriggerKey { get; set; }              // 触发按键
            public int FormationAngle { get; set; }          // 三角阵型角度
            public int MovementRadius { get; set; }          // 移动半径
            
            // 三角合球序列的时间延迟配置
            public int InitialDelay { get; set; }            // 初始延迟
            public int FirstMoveDelay { get; set; }          // 第一次移动延迟
            public int FirstSplitDelay { get; set; }         // 第一次分身延迟
            public int SecondMoveDelay { get; set; }         // 第二次移动延迟
            public int SecondSplitDelay { get; set; }        // 第二次分身延迟
            public int FinalMoveDelay { get; set; }          // 最后移动延迟
            public int FinalSplitDelay { get; set; }         // 最后分身延迟
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public TriangleMergingConfig()
            {
                // 设置三角合球的默认参数
                IsEnabled = false;                    // 默认不启用
                UseArrowDirection = false;            // 默认不使用箭头方向
                EnableBallEjection = false;           // 默认不启用吐球
                FormationAngle = 60;                  // 默认60度角
                MovementRadius = 100;                 // 默认100像素半径
                InitialDelay = 50;                    // 默认50毫秒初始延迟
                FirstMoveDelay = 50;                  // 默认50毫秒移动延迟
                FirstSplitDelay = 10;                 // 默认10毫秒分身延迟
                SecondMoveDelay = 50;                 // 默认50毫秒移动延迟
                SecondSplitDelay = 10;                // 默认10毫秒分身延迟
                FinalMoveDelay = 50;                  // 默认50毫秒移动延迟
                FinalSplitDelay = 20;                 // 默认20毫秒分身延迟
            }
        }
        #endregion

        #region 中分配置类
        
        /// <summary>
        /// 中分（原地合球）操作的配置类
        /// 用于配置从中心位置进行球体分割的参数
        /// </summary>
        public class CenterSplitConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用中分功能
            public bool EnableBallEjection { get; set; }     // 是否启用吐球功能
            public int TriggerKey { get; set; }              // 触发按键
            public int MovementRadius { get; set; }          // 移动半径
            
            // 中分序列的时间延迟配置
            public int FirstDragDelay { get; set; }          // 第一次拖拽延迟
            public int FirstSplitDelay { get; set; }         // 第一次分身延迟
            public int SecondDragDelay { get; set; }         // 第二次拖拽延迟
            public int SecondSplitDelay { get; set; }        // 第二次分身延迟
            public int ThirdDragDelay { get; set; }          // 第三次拖拽延迟
            public int FinalSplitDelay { get; set; }         // 最后分身延迟
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public CenterSplitConfig()
            {
                // 设置中分的默认参数
                IsEnabled = false;                    // 默认不启用
                EnableBallEjection = false;           // 默认不启用吐球
                MovementRadius = 100;                 // 默认100像素半径
                FirstDragDelay = 50;                  // 默认50毫秒拖拽延迟
                FirstSplitDelay = 10;                 // 默认10毫秒分身延迟
                SecondDragDelay = 50;                 // 默认50毫秒拖拽延迟
                SecondSplitDelay = 10;                // 默认10毫秒分身延迟
                ThirdDragDelay = 50;                  // 默认50毫秒拖拽延迟
                FinalSplitDelay = 20;                 // 默认20毫秒分身延迟
            }
        }
        #endregion

        #region 四分配置类
        
        /// <summary>
        /// 四分侧合操作的配置类
        /// 用于配置四分割球体合并的参数
        /// </summary>
        public class QuarterSplitConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用四分功能
            public bool EnableBallEjection { get; set; }     // 是否启用吐球功能
            public int TriggerKey { get; set; }              // 触发按键
            public int MovementRadius { get; set; }          // 移动半径
            
            // 四分序列的时间延迟配置
            public int FirstSplitDelay { get; set; }         // 第一次分身延迟
            public int SecondSplitDelay { get; set; }        // 第二次分身延迟
            public int DragDelay { get; set; }               // 拖拽延迟
            public int FinalDragDelay { get; set; }          // 最后拖拽延迟
            public int FinalSplitDelay { get; set; }         // 最后分身延迟
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public QuarterSplitConfig()
            {
                // 设置四分的默认参数
                IsEnabled = false;                    // 默认不启用
                EnableBallEjection = false;           // 默认不启用吐球
                MovementRadius = 100;                 // 默认100像素半径
                FirstSplitDelay = 10;                 // 默认10毫秒分身延迟
                SecondSplitDelay = 10;                // 默认10毫秒分身延迟
                DragDelay = 50;                       // 默认50毫秒拖拽延迟
                FinalDragDelay = 50;                  // 默认50毫秒拖拽延迟
                FinalSplitDelay = 20;                 // 默认20毫秒分身延迟
            }
        }
        #endregion

        #region 后仰配置类
        
        /// <summary>
        /// 后仰操作的配置类
        /// 用于配置后仰球体移动的参数
        /// </summary>
        public class BackwardLeanConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用后仰功能
            public bool EnableBallEjection { get; set; }     // 是否启用吐球功能
            public int TriggerKey { get; set; }              // 触发按键
            public int MovementRadius { get; set; }          // 移动半径
            public int LeanAngle { get; set; }               // 后仰角度
            
            // 后仰序列的时间延迟配置
            public int InitialSplitDelay { get; set; }       // 初始分身延迟
            public int MouseDownDelay { get; set; }          // 鼠标按下延迟
            public int FirstDragDelay { get; set; }          // 第一次拖拽延迟
            public int SecondSplitDelay { get; set; }        // 第二次分身延迟
            public int SecondDragDelay { get; set; }         // 第二次拖拽延迟
            public int FinalSplitDelay { get; set; }         // 最后分身延迟
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public BackwardLeanConfig()
            {
                // 设置后仰的默认参数
                IsEnabled = false;                    // 默认不启用
                EnableBallEjection = false;           // 默认不启用吐球
                MovementRadius = 120;                 // 默认120像素半径
                LeanAngle = 40;                       // 默认40度后仰角
                InitialSplitDelay = 10;               // 默认10毫秒初始分身延迟
                MouseDownDelay = 20;                  // 默认20毫秒鼠标按下延迟
                FirstDragDelay = 50;                  // 默认50毫秒拖拽延迟
                SecondSplitDelay = 10;                // 默认10毫秒分身延迟
                SecondDragDelay = 50;                 // 默认50毫秒拖拽延迟
                FinalSplitDelay = 20;                 // 默认20毫秒分身延迟
            }
        }
        #endregion

        #region 杠杆宏配置类
        
        /// <summary>
        /// 杠杆宏操作的配置类
        /// 用于配置杠杆宏的双击检测和执行参数
        /// </summary>
        public class LeverMacroConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用杠杆宏功能
            public int TriggerKey { get; set; }              // 触发按键
            public int DelayBetweenSplits { get; set; }      // 分身之间的延迟
            public int TimeIntervalForDoublePress { get; set; } // 双击检测的时间间隔（毫秒）
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public LeverMacroConfig()
            {
                // 设置杠杆宏的默认参数
                IsEnabled = false;                    // 默认不启用
                DelayBetweenSplits = 50;              // 默认50毫秒分身间隔
                TimeIntervalForDoublePress = 1000;    // 默认1秒双击检测间隔
            }
        }
        #endregion

        #region 旋转配置类
        
        /// <summary>
        /// 旋转操作的配置类（半旋/旋转/蛇手）
        /// 用于配置各种旋转模式的参数
        /// </summary>
        public class RotationConfig
        {
            public bool IsEnabled { get; set; }              // 是否启用旋转功能
            public bool EnableBallEjection { get; set; }     // 是否启用吐球功能
            public int TriggerKey { get; set; }              // 触发按键
            
            // 4步旋转的角度配置
            public int Angle1 { get; set; }                  // 第1步角度
            public int Angle2 { get; set; }                  // 第2步角度
            public int Angle3 { get; set; }                  // 第3步角度
            public int Angle4 { get; set; }                  // 第4步角度
            
            // 4步旋转的半径配置
            public int Radius1 { get; set; }                 // 第1步半径
            public int Radius2 { get; set; }                 // 第2步半径
            public int Radius3 { get; set; }                 // 第3步半径
            public int Radius4 { get; set; }                 // 第4步半径
            
            // 旋转序列的时间延迟配置
            public int[] TimingDelays { get; set; }          // 时间延迟数组
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public RotationConfig()
            {
                // 设置旋转的默认参数
                IsEnabled = false;                    // 默认不启用
                EnableBallEjection = false;           // 默认不启用吐球
                Angle1 = 45;                          // 默认45度角
                Angle2 = 45;                          // 默认45度角
                Angle3 = 45;                          // 默认45度角
                Angle4 = 45;                          // 默认45度角
                Radius1 = 80;                         // 默认80像素半径
                Radius2 = 80;                         // 默认80像素半径
                Radius3 = 80;                         // 默认80像素半径
                Radius4 = 80;                         // 默认80像素半径
                // 默认时间延迟序列：9个延迟值
                TimingDelays = new int[] { 50, 50, 10, 50, 10, 50, 10, 50, 20 };
            }
        }
        #endregion

        #region 全局配置类
        
        /// <summary>
        /// 全局配置设置类
        /// 包含所有操作共用的基础参数
        /// </summary>
        public class GlobalConfig
        {
            public byte SplitKey { get; set; }               // 分身按键
            public byte BallEjectionKey { get; set; }        // 吐球按键
            public float JoystickSensitivity { get; set; }   // 摇杆灵敏度
            public IntPtr WindowHandle { get; set; }         // 游戏窗口句柄
            
            /// <summary>
            /// 构造函数，设置默认值
            /// </summary>
            public GlobalConfig()
            {
                JoystickSensitivity = 1.6f;          // 默认摇杆灵敏度
            }
        }
        #endregion

        #region 配置实例
        
        // 各种操作的配置实例
        public TriangleMergingConfig TriangleMerging { get; set; }    // 三角合球1配置
        public TriangleMergingConfig TriangleMerging2 { get; set; }   // 三角合球2配置
        public CenterSplitConfig CenterSplit { get; set; }            // 中分配置
        public QuarterSplitConfig QuarterSplit { get; set; }          // 四分配置
        public BackwardLeanConfig BackwardLean { get; set; }          // 后仰配置
        public LeverMacroConfig LeverMacro { get; set; }              // 杠杆宏配置
        public RotationConfig HalfRotation { get; set; }              // 半旋配置
        public RotationConfig FullRotation { get; set; }              // 全旋配置
        public RotationConfig SnakeHand { get; set; }                 // 蛇手配置
        public GlobalConfig Global { get; set; }                      // 全局配置
        
        #endregion

        #region 构造函数
        
        /// <summary>
        /// 主配置类构造函数
        /// 初始化所有子配置对象
        /// </summary>
        public BallMergingConfiguration()
        {
            // 创建所有配置对象的实例
            TriangleMerging = new TriangleMergingConfig();    // 初始化三角合球1配置
            TriangleMerging2 = new TriangleMergingConfig();   // 初始化三角合球2配置
            CenterSplit = new CenterSplitConfig();            // 初始化中分配置
            QuarterSplit = new QuarterSplitConfig();          // 初始化四分配置
            BackwardLean = new BackwardLeanConfig();          // 初始化后仰配置
            LeverMacro = new LeverMacroConfig();              // 初始化杠杆宏配置
            HalfRotation = new RotationConfig();              // 初始化半旋配置
            FullRotation = new RotationConfig();              // 初始化全旋配置
            SnakeHand = new RotationConfig();                 // 初始化蛇手配置
            Global = new GlobalConfig();                      // 初始化全局配置
        }
        
        #endregion
    }
}
