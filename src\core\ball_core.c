/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 核心功能模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 球体操作核心功能的具体实现
 * • 包含所有数学计算、输入模拟、缩放工具等基础功能
 * • 被所有其他模块共同使用的底层函数库
 * 
 * 🎯 实现特点:
 * • 高性能的数学计算优化
 * • 精确的输入模拟机制
 * • 完善的错误处理
 * • 跨分辨率适配支持
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "ball_core.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 数学计算函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算鼠标移动方向和参数
 * 基于鼠标位置和窗口矩形计算移动方向向量
 */
bool calculate_mouse_direction(Point mouse_pos, Rectangle window_rect, 
                              double* dx, double* dy, double* slope) {
    if (!dx || !dy || !slope) {
        return false;  // 参数检查
    }
    
    // 计算窗口中心点
    double center_x = (window_rect.left + window_rect.right) / 2.0;
    double center_y = (window_rect.top + window_rect.bottom) / 2.0;
    
    // 计算相对于中心的偏移
    *dx = mouse_pos.x - center_x;
    *dy = mouse_pos.y - center_y;
    
    // 计算斜率（避免除零）
    if (fabs(*dx) < 1e-6) {
        *slope = (*dy > 0) ? 1e6 : -1e6;  // 垂直线的近似斜率
    } else {
        *slope = *dy / *dx;
    }
    
    return true;
}

/**
 * 计算方向向量
 * 将坐标转换为单位方向向量
 */
DirectionVector calculate_direction_vector(double x, double y) {
    DirectionVector result = {0.0, 0.0};
    
    // 计算向量长度
    double length = sqrt(x * x + y * y);
    
    // 避免除零，返回单位向量
    if (length > 1e-6) {
        result.x = x / length;
        result.y = y / length;
    }
    
    return result;
}

/**
 * 执行基于角度的旋转计算
 * 用于旋转类操作，如半旋、全旋等
 */
DirectionVector perform_angle_rotation(double current_angle, int angle_change, bool is_positive) {
    DirectionVector result;
    
    // 根据旋转方向计算新角度
    double new_angle = is_positive ? current_angle + angle_change : current_angle - angle_change;
    
    // 将角度转换为弧度并计算方向分量
    double angle_rad = degrees_to_radians(new_angle);
    result.x = cos(angle_rad);
    result.y = sin(angle_rad);
    
    return result;
}

/**
 * 计算三角形顶点坐标
 * 用于三角合球的顶点位置计算
 */
TriangleVertices calculate_triangle_vertices(double center_x, double center_y, 
                                           double radius, double angle) {
    TriangleVertices vertices;
    
    // 将角度转换为弧度
    double angle_rad = degrees_to_radians(angle);
    
    // 计算第一个顶点（基准点）
    vertices.vertex1.x = (int)(center_x + radius * cos(0));
    vertices.vertex1.y = (int)(center_y + radius * sin(0));
    
    // 计算第二个顶点（逆时针旋转）
    double sx2 = radius * cos(angle_rad);
    double sy2 = radius * sin(angle_rad);
    vertices.vertex2.x = (int)(center_x + sx2 * cos(angle_rad) - sy2 * sin(angle_rad));
    vertices.vertex2.y = (int)(center_y + sx2 * sin(angle_rad) + sy2 * cos(angle_rad));
    
    // 计算第三个顶点（顺时针旋转）
    double sx3 = radius * cos(-angle_rad);
    double sy3 = radius * sin(-angle_rad);
    vertices.vertex3.x = (int)(center_x + sx3 * cos(angle_rad) + sy3 * sin(angle_rad));
    vertices.vertex3.y = (int)(center_y + sx3 * sin(angle_rad) - sy3 * cos(angle_rad));
    
    return vertices;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🖱️ 输入模拟函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 模拟按键操作
 * 用于执行分身等按键操作
 */
bool simulate_key_press(BYTE key, int delay) {
    // 获取按键的扫描码
    UINT scancode = MapVirtualKey(key, 0);
    
    // 模拟按键按下
    keybd_event(key, (BYTE)scancode, 0, 0);
    
    // 模拟按键释放
    keybd_event(key, (BYTE)scancode, KEYEVENTF_KEYUP, 0);
    
    // 等待指定的延迟时间
    if (delay > 0) {
        precise_delay(delay);
    }
    
    return true;
}

/**
 * 模拟鼠标按钮操作
 * 用于执行鼠标点击操作
 */
bool simulate_mouse_button(bool is_left_button, bool is_press) {
    DWORD flags;
    
    if (is_left_button) {
        flags = is_press ? MOUSEEVENTF_LEFTDOWN : MOUSEEVENTF_LEFTUP;
    } else {
        flags = is_press ? MOUSEEVENTF_RIGHTDOWN : MOUSEEVENTF_RIGHTUP;
    }
    
    mouse_event(flags, 0, 0, 0, 0);
    return true;
}

/**
 * 模拟鼠标移动
 * 用于移动鼠标到指定位置
 */
bool simulate_mouse_move(int x, int y) {
    SetCursorPos(x, y);
    return true;
}

/**
 * 检查按键是否被按下
 * 用于检测用户输入状态
 */
bool is_key_pressed(BYTE key) {
    return (GetAsyncKeyState(key) & 0x8000) != 0;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📏 缩放工具函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 根据摇杆灵敏度缩放数值
 * 用于适配不同的摇杆灵敏度设置
 */
int scale_by_sensitivity(int value, int sensitivity) {
    // 灵敏度范围限制在1-200之间
    sensitivity = clamp_value(sensitivity, 1, 200);
    
    // 按比例缩放（100为基准）
    return (int)(value * sensitivity / 100.0);
}

/**
 * 根据窗口大小缩放坐标
 * 用于适配不同分辨率的窗口
 */
int scale_by_window_size(int value, int window_size, int reference_size) {
    if (reference_size <= 0) {
        return value;  // 避免除零
    }
    
    return (int)(value * window_size / (double)reference_size);
}

/**
 * 计算两点之间的距离
 * 用于距离计算和碰撞检测
 */
double calculate_distance(Point p1, Point p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    return sqrt(dx * dx + dy * dy);
}

/**
 * 弧度转角度
 */
double radians_to_degrees(double radians) {
    return radians * 180.0 / PI;
}

/**
 * 角度转弧度
 */
double degrees_to_radians(double degrees) {
    return degrees * PI / 180.0;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 工具函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 精确延迟函数
 * 提供比Sleep更精确的延迟
 */
void precise_delay(int milliseconds) {
    if (milliseconds <= 0) return;
    
    LARGE_INTEGER frequency, start, end;
    QueryPerformanceFrequency(&frequency);
    QueryPerformanceCounter(&start);
    
    LONGLONG target_ticks = (LONGLONG)(milliseconds * frequency.QuadPart / 1000.0);
    
    do {
        QueryPerformanceCounter(&end);
    } while ((end.QuadPart - start.QuadPart) < target_ticks);
}

/**
 * 获取当前时间戳（毫秒）
 */
uint64_t get_current_timestamp(void) {
    return GetTickCount64();
}

/**
 * 获取窗口句柄
 */
HWND get_window_handle(const char* window_title) {
    return FindWindowA(NULL, window_title);
}

/**
 * 获取窗口矩形区域
 */
bool get_window_rect(HWND hwnd, Rectangle* rect) {
    if (!hwnd || !rect) {
        return false;
    }
    
    RECT win_rect;
    if (GetWindowRect(hwnd, &win_rect)) {
        rect->left = win_rect.left;
        rect->top = win_rect.top;
        rect->right = win_rect.right;
        rect->bottom = win_rect.bottom;
        return true;
    }
    
    return false;
}

/**
 * 检查点是否在矩形内
 */
bool is_point_in_rect(Point point, Rectangle rect) {
    return (point.x >= rect.left && point.x <= rect.right &&
            point.y >= rect.top && point.y <= rect.bottom);
}

/**
 * 限制数值在指定范围内
 */
int clamp_value(int value, int min_val, int max_val) {
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return value;
}

/**
 * 限制浮点数值在指定范围内
 */
double clamp_double(double value, double min_val, double max_val) {
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return value;
}
