/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 核心功能模块头文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 球体操作核心功能的头文件定义
 * • 包含所有核心数据结构和函数声明
 * • 提供数学计算、输入模拟、缩放工具等基础功能
 * 
 * 🎯 主要功能:
 * • 🧮 数学计算函数（方向向量、角度旋转、三角形计算等）
 * • 🖱️ 输入模拟函数（按键、鼠标操作等）
 * • 📏 缩放工具函数（分辨率适配、灵敏度调节等）
 * • 🔧 工具函数（时间延迟、状态检查等）
 * 
 * 🔗 依赖关系:
 * • Windows API - 用于输入模拟和窗口操作
 * • 数学库 - 用于三角函数计算
 * 
 * ⚠️ 重要提示:
 * 这是整个系统的基础模块，修改时需要特别小心，因为会影响所有其他功能模块
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#ifndef BALL_CORE_H
#define BALL_CORE_H

#include <windows.h>
#include <math.h>
#include <stdbool.h>
#include <stdint.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define PI 3.14159265358979323846          // 圆周率常量
#define KEYEVENTF_KEYUP 0x0002             // 按键释放标志
#define MAX_PATH_LENGTH 260                // 最大路径长度
#define MAX_CONFIG_STRING 128              // 最大配置字符串长度

// 默认配置值
#define DEFAULT_DELAY 100                  // 默认延迟时间（毫秒）
#define DEFAULT_SENSITIVITY 100            // 默认摇杆灵敏度
#define DEFAULT_ANGLE 60                   // 默认角度（度）
#define DEFAULT_RADIUS 50                  // 默认半径

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 数据结构定义
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 🎯 点坐标结构
 * 用于表示屏幕上的点坐标
 */
typedef struct {
    int x;                                 // X坐标
    int y;                                 // Y坐标
} Point;

/**
 * 🎯 矩形区域结构
 * 用于表示窗口或区域的矩形范围
 */
typedef struct {
    int left;                              // 左边界
    int top;                               // 上边界
    int right;                             // 右边界
    int bottom;                            // 下边界
} Rectangle;

/**
 * 🎯 方向向量结构
 * 用于表示移动方向和速度
 */
typedef struct {
    double x;                              // X方向分量
    double y;                              // Y方向分量
} DirectionVector;

/**
 * 🎯 三角形顶点结构
 * 用于三角合球的顶点计算
 */
typedef struct {
    Point vertex1;                         // 第一个顶点
    Point vertex2;                         // 第二个顶点
    Point vertex3;                         // 第三个顶点
} TriangleVertices;

/**
 * 🎯 旋转参数结构
 * 用于旋转操作的参数传递
 */
typedef struct {
    double current_angle;                  // 当前角度
    int direction;                         // 旋转方向（1=顺时针，-1=逆时针）
    int radius;                            // 旋转半径
    int steps;                             // 旋转步数
} RotationParams;

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 数学计算函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算鼠标移动方向和参数
 * @param mouse_pos 鼠标位置
 * @param window_rect 窗口矩形
 * @param dx 输出X方向分量
 * @param dy 输出Y方向分量
 * @param slope 输出斜率
 * @return 是否计算成功
 */
bool calculate_mouse_direction(Point mouse_pos, Rectangle window_rect, 
                              double* dx, double* dy, double* slope);

/**
 * 计算方向向量
 * @param x X坐标
 * @param y Y坐标
 * @return 方向向量结构
 */
DirectionVector calculate_direction_vector(double x, double y);

/**
 * 执行基于角度的旋转计算
 * @param current_angle 当前角度（度）
 * @param angle_change 角度变化量（度）
 * @param is_positive 是否为正向旋转
 * @return 新的方向向量
 */
DirectionVector perform_angle_rotation(double current_angle, int angle_change, bool is_positive);

/**
 * 计算三角形顶点坐标
 * @param center_x 中心点X坐标
 * @param center_y 中心点Y坐标
 * @param radius 半径
 * @param angle 角度（度）
 * @return 三角形顶点结构
 */
TriangleVertices calculate_triangle_vertices(double center_x, double center_y, 
                                           double radius, double angle);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🖱️ 输入模拟函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 模拟按键操作
 * @param key 虚拟键码
 * @param delay 按键后的延迟时间（毫秒）
 * @return 是否操作成功
 */
bool simulate_key_press(BYTE key, int delay);

/**
 * 模拟鼠标按钮操作
 * @param is_left_button 是否为左键（true=左键，false=右键）
 * @param is_press 是否为按下（true=按下，false=释放）
 * @return 是否操作成功
 */
bool simulate_mouse_button(bool is_left_button, bool is_press);

/**
 * 模拟鼠标移动
 * @param x 目标X坐标
 * @param y 目标Y坐标
 * @return 是否操作成功
 */
bool simulate_mouse_move(int x, int y);

/**
 * 检查按键是否被按下
 * @param key 虚拟键码
 * @return 是否被按下
 */
bool is_key_pressed(BYTE key);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📏 缩放工具函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 根据摇杆灵敏度缩放数值
 * @param value 原始数值
 * @param sensitivity 灵敏度（0-200）
 * @return 缩放后的数值
 */
int scale_by_sensitivity(int value, int sensitivity);

/**
 * 根据窗口大小缩放坐标
 * @param value 原始坐标值
 * @param window_size 窗口大小
 * @param reference_size 参考大小
 * @return 缩放后的坐标值
 */
int scale_by_window_size(int value, int window_size, int reference_size);

/**
 * 计算两点之间的距离
 * @param p1 第一个点
 * @param p2 第二个点
 * @return 距离值
 */
double calculate_distance(Point p1, Point p2);

/**
 * 计算角度（弧度转度）
 * @param radians 弧度值
 * @return 角度值
 */
double radians_to_degrees(double radians);

/**
 * 计算角度（度转弧度）
 * @param degrees 角度值
 * @return 弧度值
 */
double degrees_to_radians(double degrees);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 工具函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 精确延迟函数
 * @param milliseconds 延迟毫秒数
 */
void precise_delay(int milliseconds);

/**
 * 获取当前时间戳（毫秒）
 * @return 时间戳
 */
uint64_t get_current_timestamp(void);

/**
 * 获取窗口句柄
 * @param window_title 窗口标题
 * @return 窗口句柄
 */
HWND get_window_handle(const char* window_title);

/**
 * 获取窗口矩形区域
 * @param hwnd 窗口句柄
 * @param rect 输出矩形区域
 * @return 是否获取成功
 */
bool get_window_rect(HWND hwnd, Rectangle* rect);

/**
 * 检查点是否在矩形内
 * @param point 点坐标
 * @param rect 矩形区域
 * @return 是否在矩形内
 */
bool is_point_in_rect(Point point, Rectangle rect);

/**
 * 限制数值在指定范围内
 * @param value 原始数值
 * @param min_val 最小值
 * @param max_val 最大值
 * @return 限制后的数值
 */
int clamp_value(int value, int min_val, int max_val);

/**
 * 限制浮点数值在指定范围内
 * @param value 原始数值
 * @param min_val 最小值
 * @param max_val 最大值
 * @return 限制后的数值
 */
double clamp_double(double value, double min_val, double max_val);

#endif // BALL_CORE_H
