/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 控件创建模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • GUI控件的创建和管理
 * • 实现截图中所有界面元素的创建
 * • 包含布局管理和控件初始化
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "main_window.h"
#include <stdio.h>
#include <commctrl.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 界面创建函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 创建所有控件
 */
bool create_all_controls(MainWindowData* window_data) {
    if (!window_data || !window_data->hwnd) {
        return false;
    }
    
    // 创建各个区域的控件
    if (!create_control_area(window_data)) {
        return false;
    }
    
    if (!create_basic_settings_area(window_data)) {
        return false;
    }
    
    if (!create_triangle_angle_area(window_data)) {
        return false;
    }
    
    if (!create_operation_mode_area(window_data)) {
        return false;
    }
    
    if (!create_macro_operation_area(window_data)) {
        return false;
    }
    
    // 创建状态栏
    window_data->status_bar = CreateWindowEx(
        0, STATUSCLASSNAME, NULL,
        WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
        0, 0, 0, 0,
        window_data->hwnd, NULL, window_data->hinstance, NULL
    );
    
    return true;
}

/**
 * 创建运行控制区域
 */
bool create_control_area(MainWindowData* window_data) {
    if (!window_data) {
        return false;
    }
    
    HWND parent = window_data->hwnd;
    HINSTANCE hinstance = window_data->hinstance;
    
    // 运行按钮
    window_data->btn_start = CreateWindow(
        L"BUTTON", L"运行",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        CONTROL_AREA_X, CONTROL_AREA_Y + 30,
        BUTTON_WIDTH, BUTTON_HEIGHT,
        parent, (HMENU)ID_BTN_START, hinstance, NULL
    );
    
    // 结束按钮
    window_data->btn_stop = CreateWindow(
        L"BUTTON", L"结束",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        CONTROL_AREA_X + BUTTON_WIDTH + 10, CONTROL_AREA_Y + 30,
        BUTTON_WIDTH, BUTTON_HEIGHT,
        parent, (HMENU)ID_BTN_STOP, hinstance, NULL
    );
    
    // 状态标签
    CreateWindow(
        L"STATIC", L"状态：正在——",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        CONTROL_AREA_X, CONTROL_AREA_Y + 70,
        150, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    return true;
}

/**
 * 创建基础设置区域
 */
bool create_basic_settings_area(MainWindowData* window_data) {
    if (!window_data) {
        return false;
    }
    
    HWND parent = window_data->hwnd;
    HINSTANCE hinstance = window_data->hinstance;
    int y_pos = SETTINGS_AREA_Y;
    
    // 合球时自动吐复选框
    window_data->check_auto_merge = CreateWindow(
        L"BUTTON", L"合球时自动吐",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        SETTINGS_AREA_X, y_pos,
        120, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_AUTO_MERGE, hinstance, NULL
    );
    
    // 分身键设置
    CreateWindow(
        L"STATIC", L"分身键",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        SETTINGS_AREA_X + 150, y_pos,
        60, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    window_data->combo_split_key = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        SETTINGS_AREA_X + 220, y_pos,
        COMBO_WIDTH, COMBO_HEIGHT * 8,
        parent, (HMENU)ID_COMBO_SPLIT_KEY, hinstance, NULL
    );
    
    // 添加分身键选项
    SendMessage(window_data->combo_split_key, CB_ADDSTRING, 0, (LPARAM)L"E");
    SendMessage(window_data->combo_split_key, CB_ADDSTRING, 0, (LPARAM)L"Q");
    SendMessage(window_data->combo_split_key, CB_ADDSTRING, 0, (LPARAM)L"W");
    SendMessage(window_data->combo_split_key, CB_ADDSTRING, 0, (LPARAM)L"R");
    SendMessage(window_data->combo_split_key, CB_SETCURSEL, 0, 0);  // 默认选择E
    
    y_pos += 35;
    
    // 吐球键设置
    CreateWindow(
        L"STATIC", L"吐球键",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        SETTINGS_AREA_X, y_pos,
        60, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    window_data->combo_eject_key = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        SETTINGS_AREA_X + 70, y_pos,
        COMBO_WIDTH, COMBO_HEIGHT * 8,
        parent, (HMENU)ID_COMBO_EJECT_KEY, hinstance, NULL
    );
    
    // 添加吐球键选项
    SendMessage(window_data->combo_eject_key, CB_ADDSTRING, 0, (LPARAM)L"Q");
    SendMessage(window_data->combo_eject_key, CB_ADDSTRING, 0, (LPARAM)L"E");
    SendMessage(window_data->combo_eject_key, CB_ADDSTRING, 0, (LPARAM)L"W");
    SendMessage(window_data->combo_eject_key, CB_ADDSTRING, 0, (LPARAM)L"R");
    SendMessage(window_data->combo_eject_key, CB_SETCURSEL, 0, 0);  // 默认选择Q
    
    y_pos += 35;
    
    // 摇杆大小设置
    CreateWindow(
        L"STATIC", L"摇杆大小/秒",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        SETTINGS_AREA_X, y_pos,
        100, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    window_data->edit_joystick_size = CreateWindow(
        L"EDIT", L"100",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_NUMBER,
        SETTINGS_AREA_X + 110, y_pos,
        EDIT_WIDTH, EDIT_HEIGHT,
        parent, (HMENU)ID_EDIT_JOYSTICK_SIZE, hinstance, NULL
    );
    
    window_data->slider_joystick = CreateWindow(
        TRACKBAR_CLASS, NULL,
        WS_CHILD | WS_VISIBLE | TBS_HORZ | TBS_AUTOTICKS,
        SETTINGS_AREA_X + 200, y_pos,
        SLIDER_WIDTH, SLIDER_HEIGHT,
        parent, (HMENU)ID_SLIDER_JOYSTICK, hinstance, NULL
    );
    
    // 设置滑杆范围
    SendMessage(window_data->slider_joystick, TBM_SETRANGE, TRUE, MAKELONG(1, 200));
    SendMessage(window_data->slider_joystick, TBM_SETPOS, TRUE, 100);
    
    y_pos += 35;
    
    // 延时时间设置
    CreateWindow(
        L"STATIC", L"延时时间/毫秒",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        SETTINGS_AREA_X, y_pos,
        100, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    window_data->edit_delay_time = CreateWindow(
        L"EDIT", L"100",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_NUMBER,
        SETTINGS_AREA_X + 110, y_pos,
        EDIT_WIDTH, EDIT_HEIGHT,
        parent, (HMENU)ID_EDIT_DELAY_TIME, hinstance, NULL
    );
    
    window_data->slider_delay = CreateWindow(
        TRACKBAR_CLASS, NULL,
        WS_CHILD | WS_VISIBLE | TBS_HORZ | TBS_AUTOTICKS,
        SETTINGS_AREA_X + 200, y_pos,
        SLIDER_WIDTH, SLIDER_HEIGHT,
        parent, (HMENU)ID_SLIDER_DELAY, hinstance, NULL
    );
    
    // 设置滑杆范围
    SendMessage(window_data->slider_delay, TBM_SETRANGE, TRUE, MAKELONG(10, 1000));
    SendMessage(window_data->slider_delay, TBM_SETPOS, TRUE, 100);
    
    return true;
}

/**
 * 创建一键三角调角度区域
 */
bool create_triangle_angle_area(MainWindowData* window_data) {
    if (!window_data) {
        return false;
    }
    
    HWND parent = window_data->hwnd;
    HINSTANCE hinstance = window_data->hinstance;
    int y_pos = TRIANGLE_AREA_Y;
    
    // 创建标题
    CreateWindow(
        L"STATIC", L"拖动滑杆改变角度",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        TRIANGLE_AREA_X, y_pos,
        200, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );
    
    y_pos += 25;
    
    // 创建三组按键+角度+滑杆
    for (int i = 0; i < 3; i++) {
        int x_offset = i * 130;
        
        // 按键号标签
        CreateWindow(
            L"STATIC", L"按键号",
            WS_CHILD | WS_VISIBLE | SS_LEFT,
            TRIANGLE_AREA_X + x_offset, y_pos,
            50, LABEL_HEIGHT,
            parent, NULL, hinstance, NULL
        );
        
        // 按键号下拉框
        window_data->combo_key[i] = CreateWindow(
            L"COMBOBOX", NULL,
            WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
            TRIANGLE_AREA_X + x_offset + 55, y_pos,
            50, COMBO_HEIGHT * 8,
            parent, (HMENU)(ID_COMBO_KEY_1 + i * 3), hinstance, NULL
        );
        
        // 添加按键选项
        wchar_t key_text[8];
        swprintf_s(key_text, 8, L"%d", i + 1);
        SendMessage(window_data->combo_key[i], CB_ADDSTRING, 0, (LPARAM)key_text);
        SendMessage(window_data->combo_key[i], CB_ADDSTRING, 0, (LPARAM)L"2");
        SendMessage(window_data->combo_key[i], CB_ADDSTRING, 0, (LPARAM)L"3");
        SendMessage(window_data->combo_key[i], CB_SETCURSEL, i, 0);
        
        // 角度标签
        CreateWindow(
            L"STATIC", L"角度",
            WS_CHILD | WS_VISIBLE | SS_LEFT,
            TRIANGLE_AREA_X + x_offset, y_pos + 30,
            30, LABEL_HEIGHT,
            parent, NULL, hinstance, NULL
        );
        
        // 角度输入框
        window_data->edit_angle[i] = CreateWindow(
            L"EDIT", i == 0 ? L"180°" : i == 1 ? L"150°" : L"100°",
            WS_CHILD | WS_VISIBLE | WS_BORDER,
            TRIANGLE_AREA_X + x_offset + 35, y_pos + 30,
            60, EDIT_HEIGHT,
            parent, (HMENU)(ID_EDIT_ANGLE_1 + i * 3), hinstance, NULL
        );
        
        // 角度滑杆
        window_data->slider_angle[i] = CreateWindow(
            TRACKBAR_CLASS, NULL,
            WS_CHILD | WS_VISIBLE | TBS_VERT | TBS_AUTOTICKS,
            TRIANGLE_AREA_X + x_offset + 100, y_pos + 30,
            25, 60,
            parent, (HMENU)(ID_SLIDER_ANGLE_1 + i * 3), hinstance, NULL
        );
        
        // 设置滑杆范围和位置
        SendMessage(window_data->slider_angle[i], TBM_SETRANGE, TRUE, MAKELONG(0, 360));
        SendMessage(window_data->slider_angle[i], TBM_SETPOS, TRUE, 
                   i == 0 ? 180 : i == 1 ? 150 : 100);
    }
    
    return true;
}

/**
 * 创建操作模式区域
 */
bool create_operation_mode_area(MainWindowData* window_data) {
    if (!window_data) {
        return false;
    }

    HWND parent = window_data->hwnd;
    HINSTANCE hinstance = window_data->hinstance;
    int y_pos = MODE_AREA_Y;

    // 直线模式
    window_data->check_straight_line = CreateWindow(
        L"BUTTON", L"直线",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X, y_pos,
        CHECKBOX_WIDTH, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_STRAIGHT_LINE, hinstance, NULL
    );

    window_data->combo_options[0] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 110, y_pos,
        60, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_STRAIGHT_OPT, hinstance, NULL
    );

    // 添加直线选项
    SendMessage(window_data->combo_options[0], CB_ADDSTRING, 0, (LPARAM)L"7");
    SendMessage(window_data->combo_options[0], CB_ADDSTRING, 0, (LPARAM)L"8");
    SendMessage(window_data->combo_options[0], CB_ADDSTRING, 0, (LPARAM)L"9");
    SendMessage(window_data->combo_options[0], CB_SETCURSEL, 0, 0);

    y_pos += 30;

    // 大炮模式
    window_data->check_cannon = CreateWindow(
        L"BUTTON", L"大炮",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X, y_pos,
        CHECKBOX_WIDTH, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_CANNON, hinstance, NULL
    );

    window_data->combo_options[1] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 110, y_pos,
        60, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_CANNON_OPT, hinstance, NULL
    );

    // 添加大炮选项
    SendMessage(window_data->combo_options[1], CB_ADDSTRING, 0, (LPARAM)L"8");
    SendMessage(window_data->combo_options[1], CB_ADDSTRING, 0, (LPARAM)L"9");
    SendMessage(window_data->combo_options[1], CB_ADDSTRING, 0, (LPARAM)L"0");
    SendMessage(window_data->combo_options[1], CB_SETCURSEL, 0, 0);

    y_pos += 30;

    // 侧合为U
    window_data->check_side_merge_u = CreateWindow(
        L"BUTTON", L"侧合为U",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X, y_pos,
        CHECKBOX_WIDTH, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_SIDE_MERGE_U, hinstance, NULL
    );

    window_data->combo_options[2] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 110, y_pos,
        60, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_SIDE_U_OPT, hinstance, NULL
    );

    // 添加侧合U选项
    SendMessage(window_data->combo_options[2], CB_ADDSTRING, 0, (LPARAM)L"U");
    SendMessage(window_data->combo_options[2], CB_ADDSTRING, 0, (LPARAM)L"I");
    SendMessage(window_data->combo_options[2], CB_ADDSTRING, 0, (LPARAM)L"O");
    SendMessage(window_data->combo_options[2], CB_SETCURSEL, 0, 0);

    y_pos += 30;

    // 侧合左I
    window_data->check_side_merge_i = CreateWindow(
        L"BUTTON", L"侧合左I",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X, y_pos,
        CHECKBOX_WIDTH, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_SIDE_MERGE_I, hinstance, NULL
    );

    window_data->combo_options[3] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 110, y_pos,
        60, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_SIDE_I_OPT, hinstance, NULL
    );

    // 添加侧合I选项
    SendMessage(window_data->combo_options[3], CB_ADDSTRING, 0, (LPARAM)L"I");
    SendMessage(window_data->combo_options[3], CB_ADDSTRING, 0, (LPARAM)L"L");
    SendMessage(window_data->combo_options[3], CB_ADDSTRING, 0, (LPARAM)L"T");
    SendMessage(window_data->combo_options[3], CB_SETCURSEL, 0, 0);

    return true;
}

/**
 * 创建宏操作区域
 */
bool create_macro_operation_area(MainWindowData* window_data) {
    if (!window_data) {
        return false;
    }

    HWND parent = window_data->hwnd;
    HINSTANCE hinstance = window_data->hinstance;
    int y_pos = MODE_AREA_Y + 150;

    // 二键一式标题
    CreateWindow(
        L"STATIC", L"二键一式",
        WS_CHILD | WS_VISIBLE | SS_LEFT,
        MODE_AREA_X, y_pos,
        80, LABEL_HEIGHT,
        parent, NULL, hinstance, NULL
    );

    y_pos += 25;

    // 侧合4
    window_data->check_side_4 = CreateWindow(
        L"BUTTON", L"侧合",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X, y_pos,
        60, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_SIDE_4, hinstance, NULL
    );

    window_data->combo_options[4] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 70, y_pos,
        50, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_SIDE_4_OPT, hinstance, NULL
    );

    SendMessage(window_data->combo_options[4], CB_ADDSTRING, 0, (LPARAM)L"4");
    SendMessage(window_data->combo_options[4], CB_SETCURSEL, 0, 0);

    // 四分5
    window_data->check_quarter_5 = CreateWindow(
        L"BUTTON", L"四分",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        MODE_AREA_X + 130, y_pos,
        60, CHECKBOX_HEIGHT,
        parent, (HMENU)ID_CHECK_QUARTER_5, hinstance, NULL
    );

    window_data->combo_options[5] = CreateWindow(
        L"COMBOBOX", NULL,
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST,
        MODE_AREA_X + 200, y_pos,
        50, COMBO_HEIGHT * 6,
        parent, (HMENU)ID_COMBO_QUARTER_5_OPT, hinstance, NULL
    );

    SendMessage(window_data->combo_options[5], CB_ADDSTRING, 0, (LPARAM)L"5");
    SendMessage(window_data->combo_options[5], CB_SETCURSEL, 0, 0);

    return true;
}
