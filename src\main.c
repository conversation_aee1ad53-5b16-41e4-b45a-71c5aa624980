/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 主程序入口文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 程序说明:
 * • 球球大作战自动合球软件的主程序入口
 * • 基于C#版本的完整功能移植到C语言
 * • 提供现代化的GUI界面和强大的合球算法
 * 
 * 🎯 主要功能:
 * • 🔺 三角合球 - 经典的三角形阵型合球
 * • ⭕ 中分合球 - 中心分割合球技术
 * • ➕ 四分合球 - 快速四分割合球
 * • 🔄 旋转合球 - 半旋/全旋/蛇手等旋转技术
 * • 🤖 宏操作 - 各种自动化宏功能
 * • ⚙️ 配置管理 - 完善的参数配置系统
 * • 🖥️ GUI界面 - 用户友好的图形界面
 * 
 * ✨ 软件特点:
 * • 高性能的C语言实现
 * • 精确的数学计算和时序控制
 * • 模块化的代码架构
 * • 完善的错误处理机制
 * • 跨分辨率适配支持
 * • 内存管理功能预留
 * 
 * 🔗 版本信息:
 * • 版本: 1.2.0
 * • 基于: C#版本的完整移植
 * • 编译器: MSVC/GCC
 * • 平台: Windows 10/11
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <locale.h>
#include <commctrl.h>
#include <shellapi.h>
#include <sddl.h>
#include <time.h>

// 包含项目头文件
#include "core/ball_core.h"
#include "config/config.h"
#include "gui/main_window.h"
#include "operations/triangle_merge.h"

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 程序常量定义
// ═══════════════════════════════════════════════════════════════════════════════════════

#define PROGRAM_VERSION "1.2.0"
#define PROGRAM_TITLE "球球大作战合球软件"
#define PROGRAM_COPYRIGHT "Copyright (C) 2024"
#define CONFIG_FILE_PATH "config/ball_merging_config.ini"
#define LOG_FILE_PATH "logs/ball_merging.log"

// 程序退出代码
#define EXIT_SUCCESS_CODE 0
#define EXIT_CONFIG_ERROR 1
#define EXIT_GUI_ERROR 2
#define EXIT_INIT_ERROR 3

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 全局变量
// ═══════════════════════════════════════════════════════════════════════════════════════

static BallMergingConfiguration g_config;     // 全局配置
static MainWindowData* g_main_window = NULL;  // 主窗口数据
static bool g_is_running = false;             // 程序运行状态
static HANDLE g_mutex = NULL;                 // 程序互斥锁

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 程序初始化和清理函数
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 检查并请求管理员权限
 * @return 是否具有管理员权限
 */
bool check_and_request_admin_privileges(void) {
    BOOL is_admin = FALSE;
    PSID admin_group = NULL;
    SID_IDENTIFIER_AUTHORITY nt_authority = SECURITY_NT_AUTHORITY;

    // 创建管理员组SID
    if (AllocateAndInitializeSid(&nt_authority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &admin_group)) {
        // 检查当前用户是否在管理员组中
        if (!CheckTokenMembership(NULL, admin_group, &is_admin)) {
            is_admin = FALSE;
        }
        FreeSid(admin_group);
    }

    if (!is_admin) {
        // 如果没有管理员权限，尝试重新启动程序并请求提权
        char exe_path[MAX_PATH];
        GetModuleFileNameA(NULL, exe_path, MAX_PATH);

        SHELLEXECUTEINFOA sei = {0};
        sei.cbSize = sizeof(SHELLEXECUTEINFOA);
        sei.lpVerb = "runas";  // 请求管理员权限
        sei.lpFile = exe_path;
        sei.lpParameters = "--elevated";  // 标记为已提权启动
        sei.nShow = SW_NORMAL;

        if (ShellExecuteExA(&sei)) {
            // 成功启动提权版本，退出当前进程
            ExitProcess(0);
        } else {
            // 用户拒绝提权或提权失败
            int result = MessageBoxA(NULL,
                "此程序需要管理员权限才能正常工作。\n"
                "某些功能可能无法使用。\n\n"
                "是否继续运行？",
                "权限提示",
                MB_YESNO | MB_ICONWARNING);

            if (result == IDNO) {
                return false;
            }
        }
    }

    return true;
}

/**
 * 初始化程序
 * @param hinstance 应用程序实例
 * @return 是否初始化成功
 */
bool initialize_program(HINSTANCE hinstance) {
    // 检查并请求管理员权限
    if (!check_and_request_admin_privileges()) {
        return false;
    }

    // 设置中文本地化
    setlocale(LC_ALL, "Chinese");
    
    // 初始化公共控件
    INITCOMMONCONTROLSEX icc;
    icc.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icc.dwICC = ICC_WIN95_CLASSES | ICC_STANDARD_CLASSES | ICC_PROGRESS_CLASS;
    if (!InitCommonControlsEx(&icc)) {
        MessageBoxA(NULL, "初始化公共控件失败！", "错误", MB_OK | MB_ICONERROR);
        return false;
    }
    
    // 检查程序是否已经运行
    g_mutex = CreateMutexA(NULL, TRUE, "BallMergingSoftwareMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        MessageBoxA(NULL, "程序已经在运行中！", "提示", MB_OK | MB_ICONINFORMATION);
        return false;
    }
    
    // 初始化配置
    if (!config_init_defaults(&g_config)) {
        MessageBoxA(NULL, "初始化配置失败！", "错误", MB_OK | MB_ICONERROR);
        return false;
    }
    
    // 尝试加载配置文件
    if (!config_load_from_file(&g_config, CONFIG_FILE_PATH)) {
        // 配置文件不存在或损坏，使用默认配置
        printf("使用默认配置\n");
    }
    
    // 验证配置
    if (!config_validate(&g_config)) {
        MessageBoxA(NULL, "配置参数验证失败，将使用默认配置！", "警告", MB_OK | MB_ICONWARNING);
        config_reset_to_defaults(&g_config);
    }
    
    return true;
}

/**
 * 清理程序资源
 */
void cleanup_program(void) {
    // 保存配置
    if (g_config.is_loaded) {
        config_save_to_file(&g_config, CONFIG_FILE_PATH);
    }
    
    // 销毁主窗口
    if (g_main_window) {
        destroy_main_window(g_main_window);
        g_main_window = NULL;
    }
    
    // 释放互斥锁
    if (g_mutex) {
        ReleaseMutex(g_mutex);
        CloseHandle(g_mutex);
        g_mutex = NULL;
    }
    
    g_is_running = false;
}

/**
 * 显示程序信息
 */
void show_program_info(void) {
    char info_text[512];
    snprintf(info_text, sizeof(info_text),
        "%s v%s\n\n"
        "基于C语言开发的高性能球球大作战合球软件\n"
        "支持多种合球技术和自动化宏操作\n\n"
        "主要功能:\n"
        "• 三角合球、中分合球、四分合球\n"
        "• 旋转合球（半旋/全旋/蛇手）\n"
        "• 各种自动化宏操作\n"
        "• 完善的配置管理系统\n"
        "• 跨分辨率适配支持\n\n"
        "%s\n"
        "技术支持: 开源社区",
        PROGRAM_TITLE, PROGRAM_VERSION, PROGRAM_COPYRIGHT
    );
    
    MessageBoxA(NULL, info_text, "关于程序", MB_OK | MB_ICONINFORMATION);
}

/**
 * 处理程序异常
 * @param exception_info 异常信息
 * @return 异常处理结果
 */
LONG WINAPI exception_handler(EXCEPTION_POINTERS* exception_info) {
    char error_msg[512];
    snprintf(error_msg, sizeof(error_msg),
        "程序发生异常！\n\n"
        "异常代码: 0x%08X\n"
        "异常地址: 0x%p\n\n"
        "程序将自动保存配置并退出。",
        exception_info->ExceptionRecord->ExceptionCode,
        exception_info->ExceptionRecord->ExceptionAddress
    );
    
    MessageBoxA(NULL, error_msg, "程序异常", MB_OK | MB_ICONERROR);
    
    // 保存配置
    if (g_config.is_loaded) {
        config_save_to_file(&g_config, CONFIG_FILE_PATH);
    }
    
    return EXCEPTION_EXECUTE_HANDLER;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎮 主程序入口函数
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * Windows程序入口点
 * @param hinstance 当前实例句柄
 * @param hprev_instance 前一个实例句柄（已废弃）
 * @param cmd_line 命令行参数
 * @param show_cmd 显示命令
 * @return 程序退出代码
 */
int WINAPI WinMain(HINSTANCE hinstance, HINSTANCE hprev_instance, LPSTR cmd_line, int show_cmd) {
    // 禁用控制台窗口（确保只有GUI界面）
    FreeConsole();
    // 设置异常处理器
    SetUnhandledExceptionFilter(exception_handler);
    
    // 初始化程序
    if (!initialize_program(hinstance)) {
        cleanup_program();
        return EXIT_INIT_ERROR;
    }
    
    // 注册主窗口类
    if (!register_main_window_class(hinstance)) {
        MessageBoxA(NULL, "注册窗口类失败！", "错误", MB_OK | MB_ICONERROR);
        cleanup_program();
        return EXIT_GUI_ERROR;
    }
    
    // 创建主窗口
    g_main_window = create_main_window(hinstance, &g_config);
    if (!g_main_window) {
        MessageBoxA(NULL, "创建主窗口失败！", "错误", MB_OK | MB_ICONERROR);
        cleanup_program();
        return EXIT_GUI_ERROR;
    }
    
    // 显示主窗口
    show_main_window(g_main_window, show_cmd);
    
    // 设置运行状态
    g_is_running = true;
    
    // 主消息循环
    MSG msg;
    while (g_is_running && GetMessage(&msg, NULL, 0, 0)) {
        // 检查是否为对话框消息
        if (!IsDialogMessage(g_main_window->hwnd, &msg)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 处理程序退出
        if (msg.message == WM_QUIT) {
            g_is_running = false;
            break;
        }
    }
    
    // 清理程序资源
    cleanup_program();
    
    return EXIT_SUCCESS_CODE;
}



// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 程序工具函数
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 获取程序版本信息
 * @return 版本字符串
 */
const char* get_program_version(void) {
    return PROGRAM_VERSION;
}

/**
 * 获取程序标题
 * @return 标题字符串
 */
const char* get_program_title(void) {
    return PROGRAM_TITLE;
}

/**
 * 检查程序是否正在运行
 * @return 是否正在运行
 */
bool is_program_running(void) {
    return g_is_running;
}

/**
 * 请求程序退出
 */
void request_program_exit(void) {
    g_is_running = false;
    if (g_main_window && g_main_window->hwnd) {
        PostMessage(g_main_window->hwnd, WM_CLOSE, 0, 0);
    }
}
