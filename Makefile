# ═══════════════════════════════════════════════════════════════════════════════════════
# 🎮 球球大作战合球软件 - Makefile编译配置文件
# ═══════════════════════════════════════════════════════════════════════════════════════
# 
# 📋 文件说明:
# • 球球大作战合球软件的编译配置
# • 支持Debug和Release两种编译模式
# • 自动处理依赖关系和资源文件
# 
# 🎯 编译目标:
# • all - 编译所有目标
# • debug - 编译调试版本
# • release - 编译发布版本
# • clean - 清理编译文件
# • install - 安装程序
# • package - 打包发布
# 
# 🔧 使用方法:
# • make          - 编译默认目标（debug）
# • make release  - 编译发布版本
# • make clean    - 清理编译文件
# • make install  - 安装程序
# ═══════════════════════════════════════════════════════════════════════════════════════

# 编译器设置
CC = gcc
WINDRES = windres

# 项目信息
PROJECT_NAME = BallMergingSoftware
VERSION = 1.2.0
TARGET_NAME = ball_merging
TARGET_EXE = $(TARGET_NAME).exe

# 目录设置
SRC_DIR = src
BUILD_DIR = build
DIST_DIR = dist
INCLUDE_DIR = include
RESOURCE_DIR = resources
CONFIG_DIR = config
LOG_DIR = logs

# 源文件
CORE_SOURCES = $(SRC_DIR)/core/ball_core.c
CONFIG_SOURCES = $(SRC_DIR)/config/config.c
GUI_SOURCES = $(SRC_DIR)/gui/main_window.c \
              $(SRC_DIR)/gui/controls.c \
              $(SRC_DIR)/gui/events.c
OPERATIONS_SOURCES = $(SRC_DIR)/operations/triangle_merge.c \
                     $(SRC_DIR)/operations/center_split.c \
                     $(SRC_DIR)/operations/quarter_split.c \
                     $(SRC_DIR)/operations/rotation.c \
                     $(SRC_DIR)/operations/backward_lean.c
MACROS_SOURCES = $(SRC_DIR)/macros/split_macros.c \
                 $(SRC_DIR)/macros/continuous.c
MEMORY_SOURCES = $(SRC_DIR)/memory/memory_mgr.c \
                 $(SRC_DIR)/memory/game_memory.c
MAIN_SOURCES = $(SRC_DIR)/main.c

# 所有源文件
ALL_SOURCES = $(CORE_SOURCES) $(CONFIG_SOURCES) $(GUI_SOURCES) \
              $(OPERATIONS_SOURCES) $(MACROS_SOURCES) $(MEMORY_SOURCES) \
              $(MAIN_SOURCES)

# 对象文件
OBJECTS = $(ALL_SOURCES:$(SRC_DIR)/%.c=$(BUILD_DIR)/%.o)

# 资源文件
RESOURCE_RC = $(RESOURCE_DIR)/app.rc
RESOURCE_OBJ = $(BUILD_DIR)/app_res.o

# 头文件搜索路径
INCLUDE_PATHS = -I$(SRC_DIR) -I$(INCLUDE_DIR)

# 库文件
LIBS = -luser32 -lgdi32 -lkernel32 -lcomctl32 -lcomdlg32 -lole32 -loleaut32 -luuid -ladvapi32

# 编译标志
COMMON_CFLAGS = $(INCLUDE_PATHS) -Wall -Wextra -std=c99
DEBUG_CFLAGS = $(COMMON_CFLAGS) -g -O0 -DDEBUG -D_DEBUG
RELEASE_CFLAGS = $(COMMON_CFLAGS) -O2 -DNDEBUG -DRELEASE

# 链接标志
COMMON_LDFLAGS = $(LIBS)
DEBUG_LDFLAGS = $(COMMON_LDFLAGS) -mconsole
RELEASE_LDFLAGS = $(COMMON_LDFLAGS) -mwindows

# 默认目标
.PHONY: all debug release clean install package help

all: debug

# 调试版本
debug: CFLAGS = $(DEBUG_CFLAGS)
debug: LDFLAGS = $(DEBUG_LDFLAGS)
debug: $(BUILD_DIR)/debug/$(TARGET_EXE)

# 发布版本
release: CFLAGS = $(RELEASE_CFLAGS)
release: LDFLAGS = $(RELEASE_LDFLAGS)
release: $(BUILD_DIR)/release/$(TARGET_EXE)

# 创建调试版本可执行文件
$(BUILD_DIR)/debug/$(TARGET_EXE): $(OBJECTS) $(RESOURCE_OBJ) | $(BUILD_DIR)/debug
	@echo "🔗 链接调试版本..."
	$(CC) -o $@ $^ $(DEBUG_LDFLAGS)
	@echo "✅ 调试版本编译完成: $@"

# 创建发布版本可执行文件
$(BUILD_DIR)/release/$(TARGET_EXE): $(OBJECTS) $(RESOURCE_OBJ) | $(BUILD_DIR)/release
	@echo "🔗 链接发布版本..."
	$(CC) -o $@ $^ $(RELEASE_LDFLAGS)
	@echo "✅ 发布版本编译完成: $@"
	@echo "📦 复制配置文件..."
	@cp -r $(CONFIG_DIR) $(BUILD_DIR)/release/ 2>/dev/null || :
	@mkdir -p $(BUILD_DIR)/release/$(LOG_DIR)

# 编译源文件为对象文件
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.c | $(BUILD_DIR)
	@echo "🔨 编译: $<"
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -c $< -o $@

# 编译资源文件
$(RESOURCE_OBJ): $(RESOURCE_RC) | $(BUILD_DIR)
	@echo "📦 编译资源文件: $<"
	$(WINDRES) $< -o $@

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(BUILD_DIR)/debug:
	@mkdir -p $(BUILD_DIR)/debug

$(BUILD_DIR)/release:
	@mkdir -p $(BUILD_DIR)/release

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@echo "✅ 清理完成"

# 安装程序
install: release
	@echo "📦 安装程序..."
	@mkdir -p $(DIST_DIR)
	@cp $(BUILD_DIR)/release/$(TARGET_EXE) $(DIST_DIR)/
	@cp -r $(BUILD_DIR)/release/$(CONFIG_DIR) $(DIST_DIR)/ 2>/dev/null || :
	@mkdir -p $(DIST_DIR)/$(LOG_DIR)
	@cp README.md $(DIST_DIR)/ 2>/dev/null || :
	@echo "✅ 安装完成: $(DIST_DIR)/"

# 打包发布
package: install
	@echo "📦 打包发布版本..."
	@cd $(DIST_DIR) && zip -r ../$(PROJECT_NAME)_v$(VERSION).zip .
	@echo "✅ 打包完成: $(PROJECT_NAME)_v$(VERSION).zip"

# 运行调试版本
run-debug: debug
	@echo "🚀 运行调试版本..."
	@cd $(BUILD_DIR)/debug && ./$(TARGET_EXE)

# 运行发布版本
run-release: release
	@echo "🚀 运行发布版本..."
	@cd $(BUILD_DIR)/release && ./$(TARGET_EXE)

# 代码格式化
format:
	@echo "🎨 格式化代码..."
	@find $(SRC_DIR) -name "*.c" -o -name "*.h" | xargs clang-format -i
	@echo "✅ 代码格式化完成"

# 静态分析
analyze:
	@echo "🔍 静态代码分析..."
	@cppcheck --enable=all --std=c99 $(SRC_DIR)
	@echo "✅ 静态分析完成"

# 生成文档
docs:
	@echo "📚 生成文档..."
	@doxygen Doxyfile 2>/dev/null || echo "需要安装Doxygen"
	@echo "✅ 文档生成完成"

# 显示帮助信息
help:
	@echo "🎮 球球大作战合球软件 - 编译帮助"
	@echo ""
	@echo "可用目标:"
	@echo "  all          - 编译默认目标（debug）"
	@echo "  debug        - 编译调试版本"
	@echo "  release      - 编译发布版本"
	@echo "  clean        - 清理编译文件"
	@echo "  install      - 安装程序到dist目录"
	@echo "  package      - 打包发布版本"
	@echo "  run-debug    - 编译并运行调试版本"
	@echo "  run-release  - 编译并运行发布版本"
	@echo "  format       - 格式化源代码"
	@echo "  analyze      - 静态代码分析"
	@echo "  docs         - 生成文档"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "编译器: $(CC)"
	@echo "项目版本: $(VERSION)"
	@echo ""

# 显示项目信息
info:
	@echo "📋 项目信息:"
	@echo "  名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  目标: $(TARGET_EXE)"
	@echo "  编译器: $(CC)"
	@echo ""
	@echo "📁 目录结构:"
	@echo "  源码目录: $(SRC_DIR)"
	@echo "  构建目录: $(BUILD_DIR)"
	@echo "  发布目录: $(DIST_DIR)"
	@echo "  配置目录: $(CONFIG_DIR)"
	@echo ""
	@echo "📄 源文件统计:"
	@echo "  核心模块: $(words $(CORE_SOURCES)) 个文件"
	@echo "  配置模块: $(words $(CONFIG_SOURCES)) 个文件"
	@echo "  GUI模块: $(words $(GUI_SOURCES)) 个文件"
	@echo "  操作模块: $(words $(OPERATIONS_SOURCES)) 个文件"
	@echo "  宏模块: $(words $(MACROS_SOURCES)) 个文件"
	@echo "  内存模块: $(words $(MEMORY_SOURCES)) 个文件"
	@echo "  总计: $(words $(ALL_SOURCES)) 个文件"

# 依赖关系
-include $(OBJECTS:.o=.d)

# 生成依赖文件
$(BUILD_DIR)/%.d: $(SRC_DIR)/%.c | $(BUILD_DIR)
	@mkdir -p $(dir $@)
	@$(CC) $(CFLAGS) -MM -MT $(@:.d=.o) $< > $@
