/**
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 🎮 球球大作战合球软件 - 三角合球操作模块实现文件
 * ═══════════════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 文件说明:
 * • 三角合球操作的具体实现
 * • 基于C#版本TriangleBallMerger的完整移植
 * • 实现精确的三角形阵型球体合并算法
 * 
 * 🎯 算法实现:
 * • 精确的三角形顶点计算
 * • 优化的移动路径规划
 * • 时序控制和分身操作
 * • 错误处理和状态管理
 * 
 * ✨ 优化特点:
 * • 高精度的数学计算
 * • 自适应的参数调整
 * • 完善的错误恢复机制
 * • 实时状态监控
 * ═══════════════════════════════════════════════════════════════════════════════════════
 */

#include "triangle_merge.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 内部辅助函数声明
// ═══════════════════════════════════════════════════════════════════════════════════════

static bool validate_triangle_context(const TriangleMergeContext* context);
static void update_execution_state(TriangleMergeContext* context, TriangleExecutionState new_state);
static bool check_execution_timeout(const TriangleMergeContext* context, uint64_t timeout_ms);
static void log_triangle_operation(const TriangleMergeContext* context, const char* message);

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 三角合球核心函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 初始化三角合球上下文
 */
bool triangle_merge_init_context(TriangleMergeContext* context,
                                const TriangleMergingConfig* triangle_config,
                                const GlobalConfig* global_config) {
    if (!context || !triangle_config || !global_config) {
        return false;
    }
    
    // 清零上下文
    memset(context, 0, sizeof(TriangleMergeContext));
    
    // 复制配置
    memcpy(&context->config, triangle_config, sizeof(TriangleMergingConfig));
    memcpy(&context->global_config, global_config, sizeof(GlobalConfig));
    
    // 初始化状态
    context->state = TRIANGLE_STATE_IDLE;
    context->is_executing = false;
    context->start_time = 0;
    context->last_operation_time = 0;
    context->current_vertex = 0;
    context->error_code = 0;
    
    // 清空错误信息
    memset(context->last_error, 0, sizeof(context->last_error));
    
    return true;
}

/**
 * 执行三角合球操作
 */
bool triangle_merge_execute(TriangleMergeContext* context,
                           Point mouse_pos,
                           Rectangle window_rect,
                           TriangleMergeResult* result) {
    if (!context || !result) {
        return false;
    }
    
    // 初始化结果
    memset(result, 0, sizeof(TriangleMergeResult));
    result->success = false;
    
    // 检查上下文有效性
    if (!validate_triangle_context(context)) {
        triangle_merge_set_error(context, -1, "上下文验证失败");
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        return false;
    }
    
    // 检查是否已在执行
    if (context->is_executing) {
        triangle_merge_set_error(context, -2, "三角合球正在执行中");
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        return false;
    }
    
    // 设置执行状态
    context->is_executing = true;
    context->start_time = get_current_timestamp();
    context->mouse_position = mouse_pos;
    context->window_rect = window_rect;
    update_execution_state(context, TRIANGLE_STATE_PREPARING);
    
    bool execution_success = false;
    
    do {
        // 第一步：执行准备阶段
        if (!triangle_merge_execute_preparation(context)) {
            triangle_merge_set_error(context, -3, "准备阶段失败");
            break;
        }
        
        // 第二步：计算三角形顶点
        update_execution_state(context, TRIANGLE_STATE_CALCULATING);
        if (!triangle_merge_calculate_vertices(context)) {
            triangle_merge_set_error(context, -4, "顶点计算失败");
            break;
        }
        
        // 第三步：执行移动和分身序列
        update_execution_state(context, TRIANGLE_STATE_MOVING);
        for (int i = 0; i < 3; i++) {
            context->current_vertex = i;
            
            // 移动到顶点
            if (!triangle_merge_execute_move_to_vertex(context, i)) {
                triangle_merge_set_error(context, -5, "移动到顶点失败");
                goto cleanup;
            }
            
            // 执行分身
            update_execution_state(context, TRIANGLE_STATE_SPLITTING);
            if (!triangle_merge_execute_split(context)) {
                triangle_merge_set_error(context, -6, "分身操作失败");
                goto cleanup;
            }
            
            result->vertices_processed++;
        }
        
        // 第四步：执行最终分身序列
        update_execution_state(context, TRIANGLE_STATE_FINALIZING);
        if (!triangle_merge_execute_final_split_sequence(context)) {
            triangle_merge_set_error(context, -7, "最终分身序列失败");
            break;
        }
        
        // 第五步：处理吐球功能
        if (context->config.enable_ball_ejection) {
            if (!triangle_merge_execute_ball_ejection(context)) {
                // 吐球失败不影响整体成功
                log_triangle_operation(context, "吐球操作失败，但不影响合球结果");
            }
        }
        
        execution_success = true;
        
    } while (false);
    
cleanup:
    // 计算执行时间
    result->execution_time = get_current_timestamp() - context->start_time;
    
    // 设置结果
    result->success = execution_success;
    result->splits_executed = result->vertices_processed * 1 + TRIANGLE_FINAL_SPLIT_COUNT;
    result->final_error_code = context->error_code;
    
    if (execution_success) {
        strcpy_s(result->result_message, sizeof(result->result_message), "三角合球执行成功");
        update_execution_state(context, TRIANGLE_STATE_IDLE);
    } else {
        strcpy_s(result->result_message, sizeof(result->result_message), context->last_error);
        update_execution_state(context, TRIANGLE_STATE_ERROR);
    }
    
    // 重置执行状态
    context->is_executing = false;
    
    return execution_success;
}

/**
 * 停止三角合球操作
 */
bool triangle_merge_stop(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }
    
    if (context->is_executing) {
        context->is_executing = false;
        update_execution_state(context, TRIANGLE_STATE_IDLE);
        triangle_merge_set_error(context, 0, "操作已被用户停止");
        return true;
    }
    
    return false;
}

/**
 * 重置三角合球上下文
 */
void triangle_merge_reset_context(TriangleMergeContext* context) {
    if (!context) {
        return;
    }
    
    // 保存配置
    TriangleMergingConfig saved_config = context->config;
    GlobalConfig saved_global_config = context->global_config;
    
    // 清零上下文
    memset(context, 0, sizeof(TriangleMergeContext));
    
    // 恢复配置
    context->config = saved_config;
    context->global_config = saved_global_config;
    
    // 重置状态
    context->state = TRIANGLE_STATE_IDLE;
    context->is_executing = false;
}

/**
 * 销毁三角合球上下文
 */
void triangle_merge_destroy_context(TriangleMergeContext* context) {
    if (!context) {
        return;
    }
    
    // 如果正在执行，先停止
    if (context->is_executing) {
        triangle_merge_stop(context);
    }
    
    // 清零上下文
    memset(context, 0, sizeof(TriangleMergeContext));
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧮 三角合球计算函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 计算三角形顶点位置
 */
bool triangle_merge_calculate_vertices(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }
    
    // 计算窗口中心点
    double center_x = (context->window_rect.left + context->window_rect.right) / 2.0;
    double center_y = (context->window_rect.top + context->window_rect.bottom) / 2.0;
    
    // 保存中心位置
    context->center_position.x = (int)center_x;
    context->center_position.y = (int)center_y;
    
    // 根据摇杆灵敏度缩放半径
    context->scaled_radius = scale_by_sensitivity(context->config.triangle_radius,
                                                 context->global_config.joystick_sensitivity);
    
    // 将角度转换为弧度
    context->formation_angle_rad = degrees_to_radians(context->config.formation_angle);
    
    // 计算三角形顶点
    context->vertices = calculate_triangle_vertices(center_x, center_y,
                                                   context->scaled_radius,
                                                   context->config.formation_angle);
    
    // 应用修正偏移
    if (context->config.correction_offset != 0) {
        for (int i = 0; i < 3; i++) {
            Point* vertex = NULL;
            switch (i) {
                case 0: vertex = &context->vertices.vertex1; break;
                case 1: vertex = &context->vertices.vertex2; break;
                case 2: vertex = &context->vertices.vertex3; break;
            }
            
            if (vertex) {
                Point corrected_pos;
                if (triangle_merge_apply_correction(context, i, &corrected_pos)) {
                    *vertex = corrected_pos;
                }
            }
        }
    }
    
    return true;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 三角合球执行函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行准备阶段
 */
bool triangle_merge_execute_preparation(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }

    // 验证参数
    if (!triangle_merge_validate_parameters(context)) {
        return false;
    }

    // 计算方向向量
    if (!triangle_merge_calculate_direction(context)) {
        triangle_merge_set_error(context, -20, "方向向量计算失败");
        return false;
    }

    // 初始延迟
    if (context->config.initial_delay > 0) {
        precise_delay(context->config.initial_delay);
    }

    // 如果启用吐球功能，按下吐球键
    if (context->config.enable_ball_ejection) {
        simulate_key_press(context->global_config.ball_eject_key, 0);
    }

    return true;
}

/**
 * 执行移动到顶点
 */
bool triangle_merge_execute_move_to_vertex(TriangleMergeContext* context, int vertex_index) {
    if (!context || vertex_index < 0 || vertex_index >= 3) {
        return false;
    }

    // 获取目标顶点位置
    Point target_pos;
    switch (vertex_index) {
        case 0: target_pos = context->vertices.vertex1; break;
        case 1: target_pos = context->vertices.vertex2; break;
        case 2: target_pos = context->vertices.vertex3; break;
        default: return false;
    }

    // 移动鼠标到目标位置
    if (!simulate_mouse_move(target_pos.x, target_pos.y)) {
        triangle_merge_set_error(context, -21, "鼠标移动失败");
        return false;
    }

    // 短暂延迟确保移动完成
    precise_delay(10);

    return true;
}

/**
 * 执行分身操作
 */
bool triangle_merge_execute_split(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }

    // 执行分身
    if (!simulate_key_press(context->global_config.split_key, context->config.split_delay)) {
        triangle_merge_set_error(context, -22, "分身操作失败");
        return false;
    }

    // 更新最后操作时间
    context->last_operation_time = get_current_timestamp();

    return true;
}

/**
 * 执行最终分身序列
 */
bool triangle_merge_execute_final_split_sequence(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }

    // 执行多次分身操作，确保球体完全合并
    for (int i = 0; i < TRIANGLE_FINAL_SPLIT_COUNT; i++) {
        if (!simulate_key_press(context->global_config.split_key, context->config.final_split_delay)) {
            triangle_merge_set_error(context, -23, "最终分身序列失败");
            return false;
        }

        // 检查是否被停止
        if (!context->is_executing) {
            return false;
        }
    }

    return true;
}

/**
 * 执行吐球操作
 */
bool triangle_merge_execute_ball_ejection(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }

    // 释放吐球按键
    UINT scancode = MapVirtualKey(context->global_config.ball_eject_key, 0);
    keybd_event(context->global_config.ball_eject_key, (BYTE)scancode, KEYEVENTF_KEYUP, 0);

    return true;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 三角合球工具函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 获取当前执行状态
 */
TriangleExecutionState triangle_merge_get_state(const TriangleMergeContext* context) {
    return context ? context->state : TRIANGLE_STATE_ERROR;
}

/**
 * 检查是否正在执行
 */
bool triangle_merge_is_executing(const TriangleMergeContext* context) {
    return context ? context->is_executing : false;
}

/**
 * 获取执行进度百分比
 */
int triangle_merge_get_progress(const TriangleMergeContext* context) {
    if (!context || !context->is_executing) {
        return 0;
    }

    switch (context->state) {
        case TRIANGLE_STATE_IDLE: return 0;
        case TRIANGLE_STATE_PREPARING: return 10;
        case TRIANGLE_STATE_CALCULATING: return 20;
        case TRIANGLE_STATE_MOVING: return 30 + (context->current_vertex * 20);
        case TRIANGLE_STATE_SPLITTING: return 35 + (context->current_vertex * 20);
        case TRIANGLE_STATE_FINALIZING: return 90;
        case TRIANGLE_STATE_ERROR: return 0;
        default: return 0;
    }
}

/**
 * 获取状态信息字符串
 */
bool triangle_merge_get_status_info(const TriangleMergeContext* context,
                                   char* buffer,
                                   size_t buffer_size) {
    if (!context || !buffer || buffer_size == 0) {
        return false;
    }

    const char* state_names[] = {
        "空闲", "准备中", "计算中", "移动中", "分身中", "完成中", "错误"
    };

    const char* state_name = (context->state >= 0 && context->state < 7) ?
                            state_names[context->state] : "未知";

    snprintf(buffer, buffer_size,
        "三角合球状态:\n"
        "当前状态: %s\n"
        "正在执行: %s\n"
        "当前顶点: %d/3\n"
        "执行进度: %d%%\n"
        "配置角度: %d度\n"
        "配置半径: %d\n"
        "启用吐球: %s\n"
        "最后错误: %s",
        state_name,
        context->is_executing ? "是" : "否",
        context->current_vertex + 1,
        triangle_merge_get_progress(context),
        context->config.formation_angle,
        context->config.triangle_radius,
        context->config.enable_ball_ejection ? "是" : "否",
        context->last_error[0] ? context->last_error : "无"
    );

    return true;
}

/**
 * 设置错误信息
 */
void triangle_merge_set_error(TriangleMergeContext* context,
                             int error_code,
                             const char* error_message) {
    if (!context) {
        return;
    }

    context->error_code = error_code;

    if (error_message) {
        strncpy_s(context->last_error, sizeof(context->last_error),
                 error_message, sizeof(context->last_error) - 1);
        context->last_error[sizeof(context->last_error) - 1] = '\0';
    } else {
        context->last_error[0] = '\0';
    }
}

/**
 * 获取最后错误信息
 */
bool triangle_merge_get_last_error(const TriangleMergeContext* context,
                                  int* error_code,
                                  char* error_message,
                                  size_t message_size) {
    if (!context) {
        return false;
    }

    if (error_code) {
        *error_code = context->error_code;
    }

    if (error_message && message_size > 0) {
        strncpy_s(error_message, message_size, context->last_error, message_size - 1);
        error_message[message_size - 1] = '\0';
    }

    return context->error_code != 0;
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔧 内部辅助函数实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 验证三角合球上下文
 */
static bool validate_triangle_context(const TriangleMergeContext* context) {
    if (!context) {
        return false;
    }

    // 检查配置有效性
    if (!context->config.is_enabled) {
        return false;
    }

    // 检查按键配置
    if (context->config.trigger_key == 0 || context->global_config.split_key == 0) {
        return false;
    }

    return true;
}

/**
 * 更新执行状态
 */
static void update_execution_state(TriangleMergeContext* context, TriangleExecutionState new_state) {
    if (!context) {
        return;
    }

    context->state = new_state;
    context->last_operation_time = get_current_timestamp();
}

/**
 * 检查执行超时
 */
static bool check_execution_timeout(const TriangleMergeContext* context, uint64_t timeout_ms) {
    if (!context) {
        return true;
    }

    uint64_t current_time = get_current_timestamp();
    return (current_time - context->start_time) > timeout_ms;
}

/**
 * 记录三角合球操作日志
 */
static void log_triangle_operation(const TriangleMergeContext* context, const char* message) {
    if (!context || !message) {
        return;
    }

    // 如果启用调试模式，输出日志
    if (context->global_config.enable_debug_mode) {
        printf("[三角合球] %s\n", message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎮 三角合球高级功能实现
// ═══════════════════════════════════════════════════════════════════════════════════════

/**
 * 执行箭头方向模式三角合球
 */
bool triangle_merge_execute_arrow_mode(TriangleMergeContext* context,
                                      DirectionVector arrow_direction,
                                      TriangleMergeResult* result) {
    if (!context || !result) {
        return false;
    }

    // 保存原始方向向量
    DirectionVector original_direction = context->direction_vector;

    // 设置箭头方向
    context->direction_vector = arrow_direction;

    // 执行三角合球
    bool success = triangle_merge_execute(context, context->mouse_position,
                                         context->window_rect, result);

    // 恢复原始方向向量
    context->direction_vector = original_direction;

    return success;
}

/**
 * 执行自适应三角合球
 */
bool triangle_merge_execute_adaptive(TriangleMergeContext* context,
                                    void* adaptive_params,
                                    TriangleMergeResult* result) {
    if (!context || !result) {
        return false;
    }

    // 自适应参数处理（当前版本使用默认实现）
    // 未来可以根据游戏状态动态调整参数

    return triangle_merge_execute(context, context->mouse_position,
                                 context->window_rect, result);
}

/**
 * 预览三角形顶点位置
 */
bool triangle_merge_preview_vertices(TriangleMergeContext* context,
                                    TriangleVertices* preview_vertices) {
    if (!context || !preview_vertices) {
        return false;
    }

    // 计算顶点位置
    if (!triangle_merge_calculate_vertices(context)) {
        return false;
    }

    // 复制顶点信息
    *preview_vertices = context->vertices;

    return true;
}

/**
 * 计算方向向量和参数
 */
bool triangle_merge_calculate_direction(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }
    
    double dx, dy, slope;
    if (!calculate_mouse_direction(context->mouse_position, context->window_rect, &dx, &dy, &slope)) {
        return false;
    }
    
    // 计算并保存方向向量
    context->direction_vector = calculate_direction_vector(dx, dy);
    
    return true;
}

/**
 * 应用位置修正
 */
bool triangle_merge_apply_correction(TriangleMergeContext* context,
                                    int vertex_index,
                                    Point* corrected_pos) {
    if (!context || !corrected_pos || vertex_index < 0 || vertex_index >= 3) {
        return false;
    }
    
    // 获取原始顶点位置
    Point original_pos;
    switch (vertex_index) {
        case 0: original_pos = context->vertices.vertex1; break;
        case 1: original_pos = context->vertices.vertex2; break;
        case 2: original_pos = context->vertices.vertex3; break;
        default: return false;
    }
    
    // 应用修正偏移
    corrected_pos->x = original_pos.x + context->config.correction_offset;
    corrected_pos->y = original_pos.y + context->config.correction_offset;
    
    // 确保修正后的位置在窗口范围内
    corrected_pos->x = clamp_value(corrected_pos->x,
                                  context->window_rect.left,
                                  context->window_rect.right);
    corrected_pos->y = clamp_value(corrected_pos->y,
                                  context->window_rect.top,
                                  context->window_rect.bottom);
    
    return true;
}

/**
 * 验证三角形参数
 */
bool triangle_merge_validate_parameters(TriangleMergeContext* context) {
    if (!context) {
        return false;
    }
    
    // 验证角度范围
    if (context->config.formation_angle < TRIANGLE_MIN_ANGLE ||
        context->config.formation_angle > TRIANGLE_MAX_ANGLE) {
        triangle_merge_set_error(context, -10, "三角形角度超出有效范围");
        return false;
    }
    
    // 验证半径范围
    if (context->config.triangle_radius < TRIANGLE_MIN_RADIUS ||
        context->config.triangle_radius > TRIANGLE_MAX_RADIUS) {
        triangle_merge_set_error(context, -11, "三角形半径超出有效范围");
        return false;
    }
    
    // 验证延迟时间
    if (context->config.initial_delay < 0 || context->config.initial_delay > 5000 ||
        context->config.split_delay < 0 || context->config.split_delay > 5000 ||
        context->config.final_split_delay < 0 || context->config.final_split_delay > 5000) {
        triangle_merge_set_error(context, -12, "延迟时间超出有效范围");
        return false;
    }
    
    // 验证修正偏移
    if (abs(context->config.correction_offset) > TRIANGLE_MAX_CORRECTION) {
        triangle_merge_set_error(context, -13, "修正偏移超出有效范围");
        return false;
    }
    
    return true;
}
